<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تصحيح الشحن</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            direction: rtl;
        }
        .debug-section {
            border: 1px solid #ccc;
            padding: 15px;
            margin: 10px 0;
            background: #f9f9f9;
        }
        .debug-title {
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        .debug-info {
            background: #fff;
            padding: 10px;
            border-left: 3px solid #007cba;
            margin: 5px 0;
        }
        .error {
            color: red;
        }
        .success {
            color: green;
        }
        button {
            background: #007cba;
            color: white;
            padding: 10px 20px;
            border: none;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #005a87;
        }
    </style>
</head>
<body>
    <h1>اختبار تصحيح مشكلة الشحن</h1>
    
    <div class="debug-section">
        <div class="debug-title">خطوات التصحيح:</div>
        <ol>
            <li>افتح أدوات المطور في المتصفح (F12)</li>
            <li>انتقل إلى تبويب Console</li>
            <li>جرب طلب مباشر وطلب من السلة</li>
            <li>راقب الرسائل في Console وفي ملف error_log</li>
        </ol>
    </div>
    
    <div class="debug-section">
        <div class="debug-title">نصائح للتصحيح:</div>
        <div class="debug-info">
            <strong>1. تحقق من ملف error_log:</strong><br>
            ابحث عن رسائل تبدأ بـ "الطلب المباشر:" أو "السلة:"
        </div>
        <div class="debug-info">
            <strong>2. تحقق من Console في المتصفح:</strong><br>
            ابحث عن رسائل "الطلب المباشر - بيانات الشحن:" و "تم تغيير طريقة الشحن:"
        </div>
        <div class="debug-info">
            <strong>3. قارن بين الطلب المباشر والسلة:</strong><br>
            لاحظ الفرق في قيم shipping_cost و shipping_method_option
        </div>
    </div>
    
    <div class="debug-section">
        <div class="debug-title">الملفات المعدلة للتصحيح:</div>
        <ul>
            <li><code>includes/class-pexlat-form-form-handler.php</code> - أضيف تسجيل للطلب المباشر</li>
            <li><code>includes/class-custom-cart-system.php</code> - أضيف تسجيل للسلة</li>
            <li><code>public/js/pexlat-form-public.js</code> - أضيف تسجيل في JavaScript</li>
        </ul>
    </div>
    
    <div class="debug-section">
        <div class="debug-title">المشكلة المحتملة:</div>
        <p>يبدو أن المشكلة في أن الطلب المباشر لا يحصل على قيمة <code>shipping_cost</code> بشكل صحيح من النموذج، بينما السلة تحسب التكلفة بطريقة مختلفة.</p>
        
        <p><strong>الحل المقترح:</strong> إضافة آلية احتياطية في الطلب المباشر للحصول على تكلفة الشحن من <code>shipping_method_option</code> إذا كانت <code>shipping_cost</code> تساوي صفر.</p>
    </div>
    
    <div class="debug-section">
        <div class="debug-title">خطوات الاختبار:</div>
        <ol>
            <li>انتقل إلى صفحة المنتج</li>
            <li>املأ النموذج واختر طريقة شحن</li>
            <li>جرب الطلب المباشر أولاً</li>
            <li>تحقق من الطلب في WooCommerce</li>
            <li>جرب إضافة للسلة ثم الطلب</li>
            <li>قارن النتائج</li>
        </ol>
    </div>
    
    <script>
        console.log('صفحة تصحيح الشحن جاهزة');
        console.log('تأكد من فتح أدوات المطور لمراقبة الرسائل');
    </script>
</body>
</html>
