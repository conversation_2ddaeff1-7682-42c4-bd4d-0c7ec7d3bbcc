# اختبار نظام الطلبات المتروكة المحسن

## الميزات الجديدة المضافة

### 1. تتبع حالة السلة
- ✅ تتبع النقر على أزرار السلة
- ✅ تأخير حفظ الطلب المتروك عند استخدام السلة
- ✅ إعادة تعيين الحالة عند إتمام الطلب

### 2. تحسين آلية حفظ الطلبات المتروكة
- ✅ تحديث نفس الطلب بدلاً من إنشاء طلبات جديدة
- ✅ البحث بناءً على رقم الهاتف + عنوان IP
- ✅ حذف الطلبات القديمة تلقائياً
- ✅ آلية beforeunload لحفظ الطلب عند مغادرة الصفحة

### 3. إعدادات تحكم جديدة
- ✅ وضع حفظ الطلب المتروك (فوري/ذكي/عند المغادرة)
- ✅ تأخير حفظ الطلب (قابل للتخصيص)
- ✅ مدة الاحتفاظ بالطلبات المتروكة

## سيناريوهات الاختبار

### السيناريو 1: الطلب المباشر (بدون سلة)
1. افتح صفحة منتج
2. املأ الحقول (الاسم، الهاتف، العنوان)
3. **النتيجة المتوقعة**: يتم حفظ الطلب المتروك فوراً (حسب الإعدادات)
4. اضغط على زر "طلب الآن"
5. **النتيجة المتوقعة**: يتم إنشاء طلب جديد وحذف المسودة

### السيناريو 2: استخدام السلة (الوضع الذكي)
1. افتح صفحة منتج
2. املأ الحقول (الاسم، الهاتف، العنوان)
3. اضغط على زر "إضافة للسلة"
4. **النتيجة المتوقعة**: لا يتم حفظ الطلب المتروك فوراً
5. انتظر 30 ثانية (أو المدة المحددة في الإعدادات)
6. **النتيجة المتوقعة**: يتم حفظ الطلب المتروك بعد التأخير
7. أكمل الطلب من السلة
8. **النتيجة المتوقعة**: يتم تحديث نفس الطلب وتغيير حالته

### السيناريو 3: مغادرة الصفحة
1. افتح صفحة منتج
2. املأ الحقول (الاسم، الهاتف، العنوان)
3. اضغط على زر "إضافة للسلة"
4. أغلق المتصفح أو انتقل لصفحة أخرى
5. **النتيجة المتوقعة**: يتم حفظ الطلب المتروك عند المغادرة

### السيناريو 4: تحديث الطلب الموجود
1. افتح صفحة منتج
2. املأ الحقول (الاسم، الهاتف، العنوان)
3. انتظر حتى يتم حفظ الطلب المتروك
4. غير بعض البيانات (مثل العنوان)
5. **النتيجة المتوقعة**: يتم تحديث نفس الطلب وليس إنشاء طلب جديد

## التحقق من النتائج

### في لوحة إدارة ووكومرس:
1. اذهب إلى "الطلبات"
2. تحقق من عدم وجود طلبات متكررة لنفس العميل
3. تحقق من أن الطلبات المتروكة لها حالة "مسودة" أو "قيد الانتظار"

### في قاعدة البيانات:
```sql
-- التحقق من خيارات المسودات
SELECT * FROM wp_options WHERE option_name LIKE 'pexlat_form_draft_%';

-- التحقق من الطلبات المتروكة
SELECT * FROM wp_posts WHERE post_type = 'shop_order' AND post_status IN ('draft', 'pending');
```

### في سجلات الأخطاء:
- تحقق من رسائل التصحيح في ملف error.log
- ابحث عن رسائل مثل "تم إنشاء/تحديث الطلب" و "تم حذف مفتاح المسودة"

## الإعدادات الموصى بها

### للمتاجر الصغيرة:
- وضع الحفظ: **ذكي**
- تأخير الحفظ: **30 ثانية**
- مدة الاحتفاظ: **24 ساعة**

### للمتاجر الكبيرة:
- وضع الحفظ: **ذكي**
- تأخير الحفظ: **60 ثانية**
- مدة الاحتفاظ: **12 ساعة**

### للمتاجر التي تفضل عدم حفظ الطلبات إلا عند الضرورة:
- وضع الحفظ: **عند المغادرة فقط**
- مدة الاحتفاظ: **6 ساعات**

## المشاكل المحلولة

1. ✅ **طلبات متكررة**: لا يتم إنشاء طلبات جديدة عند استخدام السلة
2. ✅ **طلبات غير مكتملة**: يتم حفظ الطلبات عند مغادرة الصفحة
3. ✅ **تراكم الطلبات**: يتم حذف الطلبات القديمة تلقائياً
4. ✅ **عدم تحديث البيانات**: يتم تحديث نفس الطلب عند تغيير البيانات

## ملاحظات مهمة

- تأكد من تفعيل إعدادات الطلبات المتروكة في لوحة الإدارة
- يمكن تخصيص الإعدادات حسب احتياجات المتجر
- يتم تنظيف الطلبات القديمة تلقائياً حسب المدة المحددة
- النظام يدعم جميع أنواع المنتجات (بسيطة ومتغيرة)
