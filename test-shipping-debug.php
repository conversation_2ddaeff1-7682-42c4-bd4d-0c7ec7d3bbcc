<?php
/**
 * ملف اختبار لتصحيح مشكلة تكلفة الشحن
 */

// تفعيل تسجيل الأخطاء
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// تحميل WordPress
require_once('wp-config.php');

// التحقق من وجود WooCommerce
if (!class_exists('WooCommerce')) {
    die('WooCommerce غير مثبت');
}

echo "<h1>اختبار تصحيح مشكلة تكلفة الشحن</h1>";

// اختبار إنشاء طلب بسيط
echo "<h2>اختبار إنشاء طلب بسيط</h2>";

try {
    // إنشاء طلب جديد
    $order = wc_create_order();
    
    if (!$order) {
        echo "فشل في إنشاء الطلب<br>";
    } else {
        echo "تم إنشاء الطلب بنجاح - معرف الطلب: " . $order->get_id() . "<br>";
        
        // إضافة منتج للطلب
        $product_id = 1; // استخدم معرف منتج موجود
        $product = wc_get_product($product_id);
        
        if ($product) {
            echo "تم العثور على المنتج: " . $product->get_name() . "<br>";
            echo "سعر المنتج: " . $product->get_price() . "<br>";
            
            // إضافة المنتج للطلب
            $item = new WC_Order_Item_Product();
            $item->set_product($product);
            $item->set_quantity(1);
            $item->set_subtotal($product->get_price());
            $item->set_total($product->get_price());
            $order->add_item($item);
            
            echo "تم إضافة المنتج للطلب<br>";
            
            // إضافة تكلفة شحن
            $shipping_cost = 500; // 500 دج
            if ($shipping_cost > 0) {
                $shipping_item = new WC_Order_Item_Shipping();
                $shipping_item->set_method_title('توصيل للمنزل');
                $shipping_item->set_total($shipping_cost);
                $order->add_item($shipping_item);
                
                echo "تم إضافة تكلفة الشحن: " . $shipping_cost . " دج<br>";
            }
            
            // حساب المجاميع
            $order->calculate_totals();
            
            echo "المجموع الفرعي: " . $order->get_subtotal() . " دج<br>";
            echo "تكلفة الشحن: " . $order->get_shipping_total() . " دج<br>";
            echo "الإجمالي: " . $order->get_total() . " دج<br>";
            
            // حفظ الطلب
            $order->save();
            echo "تم حفظ الطلب بنجاح<br>";
            
        } else {
            echo "لم يتم العثور على المنتج بمعرف: " . $product_id . "<br>";
        }
    }
    
} catch (Exception $e) {
    echo "خطأ: " . $e->getMessage() . "<br>";
}

echo "<h2>اختبار إعدادات الشحن</h2>";

// اختبار إعدادات الشحن
$form_settings = get_option('pexlat_form_settings', array());
echo "إعدادات النموذج:<br>";
echo "default_shipping_1_cost: " . (isset($form_settings['default_shipping_1_cost']) ? $form_settings['default_shipping_1_cost'] : 'غير محدد') . "<br>";
echo "default_shipping_2_cost: " . (isset($form_settings['default_shipping_2_cost']) ? $form_settings['default_shipping_2_cost'] : 'غير محدد') . "<br>";

$shipping_companies_data = get_option('pexlat_form_shipping_companies_data', array());
echo "بيانات شركات الشحن: " . (empty($shipping_companies_data) ? 'فارغة' : 'موجودة') . "<br>";

if (!empty($shipping_companies_data)) {
    foreach ($shipping_companies_data as $company_id => $company_data) {
        echo "شركة: " . $company_id . "<br>";
        if (isset($company_data['pricing']) && is_array($company_data['pricing'])) {
            foreach ($company_data['pricing'] as $pricing_item) {
                if (isset($pricing_item['price'])) {
                    echo "  - السعر: " . $pricing_item['price'] . "<br>";
                }
            }
        }
    }
}

echo "<h2>اختبار محاكاة طلب مباشر</h2>";

// محاكاة بيانات طلب مباشر
$_POST = array(
    'action' => 'pexlat_form_submit',
    'form_id' => 1,
    'product_id' => 1,
    'quantity' => 1,
    'full_name' => 'اختبار المستخدم',
    'phone' => '0123456789',
    'address' => 'عنوان اختبار',
    'state' => '16',
    'municipality' => '1601',
    'shipping_method' => 'standard_shipping',
    'shipping_cost' => '500',
    'shipping_method_option' => 'standard_shipping:500'
);

echo "بيانات الطلب المحاكاة:<br>";
foreach ($_POST as $key => $value) {
    echo $key . ": " . $value . "<br>";
}

echo "<h2>انتهى الاختبار</h2>";
?>
