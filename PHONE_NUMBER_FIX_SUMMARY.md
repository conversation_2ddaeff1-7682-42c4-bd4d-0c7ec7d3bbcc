# ملخص إصلاح مشكلة رقم الهاتف

## المشكلة
عند تمرير رقم الهاتف يظهر بدون صفر في البداية (مثل: 766554433) بدلاً من (0766554433).

## السبب
المشكلة تحدث عندما يتم التعامل مع رقم الهاتف كرقم صحيح (integer) بدلاً من نص (string)، مما يؤدي إلى فقدان الصفر في البداية.

## الحلول المطبقة

### 1. في الطلب المباشر (`includes/class-pexlat-form-form-handler.php`)

#### أ. معالجة رقم الهاتف في الطلب المباشر:
```php
// Get phone number
$phone = isset($_POST['phone']) ? sanitize_text_field($_POST['phone']) : '';
if (empty($phone) && isset($form_data['phone']) && isset($form_data['phone']['value'])) {
    $phone = $form_data['phone']['value'];
}

// تسجيل رقم الهاتف للتصحيح
error_log('الطلب المباشر: رقم الهاتف الأصلي من POST = ' . (isset($_POST['phone']) ? $_POST['phone'] : 'غير موجود'));
error_log('الطلب المباشر: رقم الهاتف بعد المعالجة = ' . $phone);

// التأكد من أن رقم الهاتف يبدأ بصفر إذا كان رقم جزائري
if (!empty($phone) && is_numeric($phone) && strlen($phone) == 9 && substr($phone, 0, 1) !== '0') {
    $phone = '0' . $phone;
    error_log('الطلب المباشر: تم إضافة الصفر لرقم الهاتف = ' . $phone);
}
```

#### ب. معالجة رقم الهاتف في حفظ المسودة:
```php
// معالجة خاصة لرقم الهاتف
if ($key === 'phone' && !empty($sanitized_value)) {
    // التأكد من أن رقم الهاتف يبدأ بصفر إذا كان رقم جزائري
    if (is_numeric($sanitized_value) && strlen($sanitized_value) == 9 && substr($sanitized_value, 0, 1) !== '0') {
        $sanitized_value = '0' . $sanitized_value;
        error_log('حفظ المسودة: تم إضافة الصفر لرقم الهاتف = ' . $sanitized_value);
    }
}
```

### 2. في نظام السلة (`includes/class-custom-cart-system.php`)

```php
// تحويل أسماء الحقول لتتطابق مع النظام المباشر
$full_name = isset($customer_data['full_name']) ? $customer_data['full_name'] : '';
$phone = isset($customer_data['phone']) ? $customer_data['phone'] : '';
$address = isset($customer_data['address']) ? $customer_data['address'] : '';

// تسجيل رقم الهاتف للتصحيح (السلة)
error_log('السلة: رقم الهاتف الأصلي = ' . $phone);

// التأكد من أن رقم الهاتف يبدأ بصفر إذا كان رقم جزائري
if (!empty($phone) && is_numeric($phone) && strlen($phone) == 9 && substr($phone, 0, 1) !== '0') {
    $phone = '0' . $phone;
    error_log('السلة: تم إضافة الصفر لرقم الهاتف = ' . $phone);
}
```

### 3. في JavaScript (`public/js/pexlat-form-public.js`)

```javascript
// معالجة رقم الهاتف للتأكد من وجود الصفر في البداية
var $phoneField = $(this).find('input[name="phone"]');
if ($phoneField.length > 0) {
    var phoneValue = $phoneField.val();
    console.log('رقم الهاتف الأصلي:', phoneValue);
    
    // إذا كان الرقم يحتوي على 9 أرقام فقط ولا يبدأ بصفر، أضف الصفر
    if (phoneValue && /^\d{9}$/.test(phoneValue) && !phoneValue.startsWith('0')) {
        phoneValue = '0' + phoneValue;
        $phoneField.val(phoneValue);
        console.log('تم إضافة الصفر لرقم الهاتف:', phoneValue);
    }
}
```

## آلية العمل

### 1. **التحقق من الشروط:**
- الرقم يحتوي على أرقام فقط (`is_numeric`)
- طول الرقم 9 أرقام (`strlen($phone) == 9`)
- لا يبدأ بصفر (`substr($phone, 0, 1) !== '0'`)

### 2. **إضافة الصفر:**
- إذا تحققت الشروط، يتم إضافة '0' في بداية الرقم
- يتم تسجيل العملية في error_log للتصحيح

### 3. **نقاط التطبيق:**
- **JavaScript:** قبل إرسال النموذج
- **PHP - الطلب المباشر:** عند معالجة البيانات
- **PHP - السلة:** عند معالجة بيانات العميل
- **PHP - المسودة:** عند حفظ البيانات الجزئية

## رسائل التصحيح

ستظهر الرسائل التالية في error_log:

```
الطلب المباشر: رقم الهاتف الأصلي من POST = 766554433
الطلب المباشر: رقم الهاتف بعد المعالجة = 766554433
الطلب المباشر: تم إضافة الصفر لرقم الهاتف = 0766554433
```

```
السلة: رقم الهاتف الأصلي = 766554433
السلة: تم إضافة الصفر لرقم الهاتف = 0766554433
```

```
حفظ المسودة: تم إضافة الصفر لرقم الهاتف = 0766554433
```

## خطوات الاختبار

1. **افتح أدوات المطور (F12)**
2. **أدخل رقم هاتف بدون صفر** (مثل: 766554433)
3. **اضغط على زر الطلب المباشر أو أضف للسلة**
4. **راقب رسائل Console:**
   - يجب أن ترى "تم إضافة الصفر لرقم الهاتف"
5. **تحقق من الطلب في WooCommerce:**
   - يجب أن يظهر الرقم مع الصفر (0766554433)
6. **تحقق من error_log:**
   - يجب أن ترى رسائل التصحيح

## النتيجة المتوقعة

- ✅ أرقام الهاتف تظهر مع الصفر في البداية
- ✅ يعمل الحل مع الطلب المباشر والسلة
- ✅ يعمل الحل مع حفظ المسودات
- ✅ رسائل تصحيح واضحة لمتابعة العملية

## ملاحظات

- الحل يتعامل فقط مع الأرقام الجزائرية (9 أرقام)
- لا يؤثر على الأرقام التي تبدأ بصفر أصلاً
- يمكن إزالة رسائل التصحيح بعد التأكد من عمل الحل
