# ملخص إصلاح مشكلة تكلفة الشحن

## المشكلة
عند الطلب من خلال زر الطلب العادي (المباشر) لا يظهر السعر الإجمالي ويظهر 0 دج في بيانات الطلب في WooCommerce، بينما عند الطلب من خلال السلة يتم تمرير السعر بشكل صحيح.

## السبب المحتمل
الطلب المباشر يعتمد على الحقول المخفية `shipping_cost` و `shipping_method` التي يتم تحديثها عند تغيير طريقة الشحن في JavaScript، بينما السلة تستخدم منطق مختلف لحساب تكلفة الشحن.

## التغييرات المطبقة

### 1. إضافة تسجيل للتصحيح

#### في `includes/class-pexlat-form-form-handler.php`:
- إضافة تسجيل لقيم الشحن المستلمة من النموذج
- إضافة تسجيل لتفاصيل الطلب بعد حساب المجاميع

#### في `includes/class-custom-cart-system.php`:
- إضافة تسجيل لتفاصيل طلب السلة للمقارنة

#### في `public/js/pexlat-form-public.js`:
- إضافة تسجيل لبيانات الشحن قبل إرسال النموذج
- إضافة تسجيل عند تغيير طريقة الشحن

### 2. إضافة آليات احتياطية لحساب تكلفة الشحن

#### في `includes/class-pexlat-form-form-handler.php`:

1. **استخراج التكلفة من `shipping_method_option`:**
   - إذا كانت `shipping_cost` تساوي 0، يتم محاولة استخراج التكلفة من `shipping_method_option`
   - دعم صيغة "company_id:cost" أو رقم مباشر

2. **استخدام منطق السلة:**
   - استخدام نفس دالة حساب تكلفة الشحن المستخدمة في السلة
   - استخدام Reflection للوصول للدالة الخاصة

3. **استخدام الإعدادات الافتراضية:**
   - كحل أخير، استخدام تكاليف الشحن الافتراضية من إعدادات النموذج

## ملفات الاختبار المضافة

### `debug-shipping-simple.html`
صفحة HTML بسيطة تحتوي على:
- تعليمات التصحيح
- نصائح لمراقبة الرسائل
- خطوات الاختبار

### `test-shipping-debug.php`
ملف PHP لاختبار:
- إنشاء طلب بسيط
- فحص إعدادات الشحن
- محاكاة طلب مباشر

## خطوات الاختبار

1. **افتح أدوات المطور في المتصفح (F12)**
2. **انتقل إلى تبويب Console**
3. **جرب طلب مباشر:**
   - املأ النموذج
   - اختر طريقة شحن
   - اضغط على زر الطلب المباشر
   - راقب الرسائل في Console
4. **جرب طلب من السلة:**
   - أضف للسلة
   - أكمل الطلب
   - راقب الرسائل في Console
5. **تحقق من ملف error_log:**
   - ابحث عن رسائل تبدأ بـ "الطلب المباشر:" أو "السلة:"
6. **قارن النتائج في WooCommerce**

## الرسائل المتوقعة في error_log

### للطلب المباشر:
```
الطلب المباشر: shipping_method = [طريقة الشحن]
الطلب المباشر: shipping_cost = [تكلفة الشحن]
الطلب المباشر: shipping_method_option = [خيار طريقة الشحن]
الطلب المباشر: تكلفة الشحن النهائية = [التكلفة النهائية]
الطلب المباشر: إجمالي الطلب بعد الحساب = [الإجمالي]
```

### للسلة:
```
السلة: إجمالي الطلب بعد الحساب = [الإجمالي]
السلة: المجموع الفرعي = [المجموع الفرعي]
السلة: تكلفة الشحن = [تكلفة الشحن]
```

## النتيجة المتوقعة

بعد تطبيق هذه التغييرات، يجب أن:
1. يظهر السعر الإجمالي بشكل صحيح في الطلب المباشر
2. تتطابق تكلفة الشحن بين الطلب المباشر والسلة
3. تظهر رسائل التصحيح في error_log و Console

## ملاحظات

- التغييرات تتضمن آليات احتياطية متعددة لضمان الحصول على تكلفة الشحن
- رسائل التصحيح ستساعد في تحديد المشكلة بدقة
- يمكن إزالة رسائل التصحيح بعد حل المشكلة
