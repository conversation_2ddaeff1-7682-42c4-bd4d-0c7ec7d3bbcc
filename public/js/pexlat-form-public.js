/**
 * Public JavaScript for Form Elrakami
 * تم تعديله لإزالة اعتماد Select2
 */
jQuery(document).ready(function($) {

    // Find all forms on the page
    var $forms = $('.pexlat-form-form');

    /**
     * تجديد رمز الأمان وإعادة إرسال النموذج
     */
    function refreshNonceAndRetry($form, callback, retryCount) {
        retryCount = retryCount || 0;
        var maxRetries = 3;

        if (retryCount >= maxRetries) {
            console.error('تم الوصول للحد الأقصى من محاولات تجديد رمز الأمان');
            callback(false);
            return;
        }

        // التحقق من توفر المتغير العام
        var ajaxUrl = (typeof formElrakami !== 'undefined' && formElrakami.ajaxurl) ?
                      formElrakami.ajaxurl :
                      (typeof ajaxurl !== 'undefined' ? ajaxurl : '/wp-admin/admin-ajax.php');

        $.ajax({
            url: ajaxUrl,
            type: 'POST',
            data: {
                action: 'pexlat_form_refresh_nonce'
            },
            timeout: 10000, // 10 ثواني timeout
            success: function(response) {
                if (response.success && response.data.nonce) {
                    // تحديث رمز الأمان في النموذج
                    $form.find('input[name="nonce"]').val(response.data.nonce);

                    // تحديث رمز الأمان في المتغير العام
                    if (typeof formElrakami !== 'undefined') {
                        formElrakami.nonce = response.data.nonce;
                    }

                    console.log('تم تجديد رمز الأمان بنجاح (المحاولة ' + (retryCount + 1) + ')');

                    // انتظار قصير قبل إعادة الإرسال لتجنب مشاكل الكاش
                    setTimeout(function() {
                        // إعادة إرسال النموذج
                        $form.trigger('submit');
                        callback(true);
                    }, 500);
                } else {
                    console.error('فشل في تجديد رمز الأمان (المحاولة ' + (retryCount + 1) + ')');

                    // إعادة المحاولة بعد تأخير
                    setTimeout(function() {
                        refreshNonceAndRetry($form, callback, retryCount + 1);
                    }, 1000 * (retryCount + 1)); // تأخير متزايد
                }
            },
            error: function() {
                console.error('خطأ في طلب تجديد رمز الأمان (المحاولة ' + (retryCount + 1) + ')');

                // إعادة المحاولة بعد تأخير
                setTimeout(function() {
                    refreshNonceAndRetry($form, callback, retryCount + 1);
                }, 1000 * (retryCount + 1)); // تأخير متزايد
            }
        });
    }

    // If forms exist, initialize them
    if ($forms.length > 0) {
       initForms();
       initStateAndShippingFields();
       initStickyBar();

       // التحقق من وضع الأيقونات (تصحيح محتمل)

       // تجديد رمز الأمان كل 20 دقيقة لتجنب انتهاء الصلاحية
       // فقط إذا كانت النماذج موجودة والمتغير العام متوفر
       if (typeof formElrakami !== 'undefined' && formElrakami.ajaxurl) {
           setInterval(function() {
               refreshNonceForAllForms();
           }, 20 * 60 * 1000); // 20 دقيقة
       }
    }

    /**
     * تجديد رمز الأمان لجميع النماذج في الصفحة
     */
    function refreshNonceForAllForms() {
        // التحقق من توفر المتغير العام
        var ajaxUrl = (typeof formElrakami !== 'undefined' && formElrakami.ajaxurl) ?
                      formElrakami.ajaxurl :
                      (typeof ajaxurl !== 'undefined' ? ajaxurl : '/wp-admin/admin-ajax.php');

        $.ajax({
            url: ajaxUrl,
            type: 'POST',
            data: {
                action: 'pexlat_form_refresh_nonce'
            },
            success: function(response) {
                if (response.success && response.data.nonce) {
                    // تحديث رمز الأمان في جميع النماذج
                    $forms.each(function() {
                        $(this).find('input[name="nonce"]').val(response.data.nonce);
                    });

                    // تحديث رمز الأمان في المتغير العام
                    if (typeof formElrakami !== 'undefined') {
                        formElrakami.nonce = response.data.nonce;
                    }

                    console.log('تم تجديد رمز الأمان تلقائياً لجميع النماذج');
                }
            },
            error: function() {
                console.error('فشل في التجديد التلقائي لرمز الأمان');
            }
        });
    }

    /**
     * مؤقت لحفظ مسودة النموذج
     */
    var draftSaveTimer = null;
    var draftSaveDelay = 5000; // 5 ثواني بين كل محاولة حفظ
    var lastSavedData = null;
    var isDraftSaving = false;
    var fieldChangeCount = 0; // عداد للتغييرات في الحقول

    // متغيرات لتتبع حالة السلة والطلبات المتروكة
    var isUsingCart = false;
    var cartButtonClicked = false;
    var abandonedOrderSaveTimeout;
    var hasUnsavedChanges = false;
    var pageUnloading = false;

    /**
     * التحقق من طرق الشحن المفعلة وإخفاء القسم إذا كانت هناك طريقة واحدة فقط
     */
    function checkShippingMethodsVisibility() {
        $forms.each(function() {
            var $form = $(this);



            var shipping1Enabled = parseInt(formElrakami.default_shipping_1_enabled) === 1;
            var shipping2Enabled = parseInt(formElrakami.default_shipping_2_enabled) === 1;

            // تحضير مصفوفة لطرق الشحن المفعلة
            var enabledMethods = [];

            // إضافة طريقة الشحن الأولى إذا كانت مفعلة
            if (shipping1Enabled) {
                enabledMethods.push({
                    value: 'standard_shipping',
                    title: formElrakami.default_shipping_1_title,
                    cost: formElrakami.default_shipping_1_cost
                });
            }

            // إضافة طريقة الشحن الثانية إذا كانت مفعلة
            if (shipping2Enabled) {
                enabledMethods.push({
                    value: 'economy_shipping',
                    title: formElrakami.default_shipping_2_title,
                    cost: formElrakami.default_shipping_2_cost
                });
            }



            // التحقق مما إذا كانت هناك طريقة شحن واحدة فقط
            if (enabledMethods.length === 1) {
                // إخفاء قسم طرق التوصيل بالكامل
                var $shippingSection = $form.find('.pexlat-form-address-fields');
                $shippingSection.hide();

                // إنشاء حقل مخفي لطريقة الشحن
                var method = enabledMethods[0];
                $form.find('input[name="shipping_method_option"]').remove();
                $form.append('<input type="hidden" name="shipping_method_option" value="' + method.value + '" data-cost="' + method.cost + '">');

                // تحديث حقول الشحن
                $form.find('input[name="shipping_cost"]').val(method.cost);
                $form.find('input[name="shipping_method"]').val(method.value);

                // حفظ اسم طريقة الشحن في حقل مخفي
                if ($form.find('input[name="shipping_method_name"]').length === 0) {
                    $form.append('<input type="hidden" name="shipping_method_name" value="' + method.title + '">');
                } else {
                    $form.find('input[name="shipping_method_name"]').val(method.title);
                }

                // تحديث السعر الإجمالي
                updateTotalPrice($form);
            }
        });
    }

    /**
     * Initialize all forms on the page
     */
    function initForms() {
        // التحقق من طرق الشحن المفعلة وإخفاء القسم إذا كانت هناك طريقة واحدة فقط
        checkShippingMethodsVisibility();

        $forms.each(function() {
            var $form = $(this);

            // قراءة إعدادات النموذج من data attribute
            var settings = {};
            try {
                settings = $form.data('settings') || {};
            } catch (e) {
                console.error('خطأ في قراءة إعدادات النموذج:', e);
                settings = {};
            }



            // التحقق من حالة إظهار عنصر الكمية
            if (settings.show_quantity !== 'hide') {
                // تهيئة أزرار التحكم في الكمية فقط إذا كانت مفعلة
                initFormQuantityControls($form);
                $form.removeClass('hide-quantity');
            } else {
                // إضافة الفئة لإخفاء عنصر الكمية
                $form.addClass('hide-quantity');
            }

            // تطبيق إعدادات منع الإكمال التلقائي
            if (formElrakami.disable_autocomplete === '1') {
                $form.attr('autocomplete', 'off');
                $form.find('input, textarea, select').attr('autocomplete', 'off');
            }

            // تطبيق إعدادات منع النسخ واللصق
            if (formElrakami.disable_copy_paste === '1') {
                $form.find('input, textarea').on('cut copy paste', function(e) {
                    e.preventDefault();
                });
            }
        });

        $forms.each(function() {
            var $form = $(this);
            var productId = $form.data('product-id');
            var formId = $form.find('input[name="form_id"]').val();
            var $message = $form.find('.pexlat-form-message');
            var $loading = $form.find('.pexlat-form-loading');

            // Make sure product ID is available in the form if it exists
            if (productId > 0 && $form.find('input[name="product_id"]').length === 0) {
                $form.append('<input type="hidden" name="product_id" value="' + productId + '">');
            }

            // Form submission
            $form.on('submit', function(e) {
                e.preventDefault();

                // التحقق من صحة النموذج قبل إرسال الطلب
                if (!validateForm($form)) {
                    return;
                }

                // Update product ID in case it has changed
                productId = $form.data('product-id') || $form.find('input[name="product_id"]').val() || 0;

                // Try to get product ID from page if missing
                if (productId <= 0) {
                    // Try to get from the URL if we're on a product page
                    var urlParams = new URLSearchParams(window.location.search);
                    var productParam = urlParams.get('product');

                    if (productParam && !isNaN(parseInt(productParam))) {
                        productId = parseInt(productParam);

                        // Update or add product ID field
                        if ($form.find('input[name="product_id"]').length > 0) {
                            $form.find('input[name="product_id"]').val(productId);
                        } else {
                            $form.append('<input type="hidden" name="product_id" value="' + productId + '">');
                        }
                    }

                    // Try to get from current page info if it's a product
                    if (productId <= 0 && typeof wc_single_product_params !== 'undefined') {
                        if (wc_single_product_params.product_id) {
                            productId = parseInt(wc_single_product_params.product_id);

                            // Update or add product ID field
                            if ($form.find('input[name="product_id"]').length > 0) {
                                $form.find('input[name="product_id"]').val(productId);
                            } else {
                                $form.append('<input type="hidden" name="product_id" value="' + productId + '">');
                            }
                        }
                    }
                }

                // Validate form
                if (!validateForm($form)) {
                    return false;
                }

                // Clear previous messages
                $message.empty().removeClass('error success');

                // Show loading spinner
                $loading.show();

                // Collect form data
                var formData = $(this).serialize();

                // Ensure action is included
                if (formData.indexOf('action=') === -1) {
                    formData += '&action=pexlat_form_submit';
                }

                // تسجيل بيانات الشحن للتصحيح
                var shippingCost = $(this).find('input[name="shipping_cost"]').val();
                var shippingMethod = $(this).find('input[name="shipping_method"]').val();
                var shippingMethodOption = $(this).find('input[name="shipping_method_option"]:checked').val() || $(this).find('input[name="shipping_method_option"]').val();

                console.log('الطلب المباشر - بيانات الشحن:');
                console.log('shipping_cost:', shippingCost);
                console.log('shipping_method:', shippingMethod);
                console.log('shipping_method_option:', shippingMethodOption);
                console.log('Form data:', formData);



                // AJAX request
                $.ajax({
                    url: formElrakami.ajaxurl,
                    type: 'POST',
                    data: formData,
                    dataType: 'json',
                    timeout: 60000, // زيادة مهلة الانتظار إلى 60 ثانية
                    success: function(response) {
                        $loading.hide();



                        if (response.success) {
                            var successMessage = response.data.message || 'تم إرسال طلبك بنجاح.';

                            // ترجمة رسالة النجاح إذا كانت متوفرة
                            if (typeof formElrakami !== 'undefined' && formElrakami.translations) {
                                if (successMessage === 'تم إرسال طلبك بنجاح.' && formElrakami.translations['تم إرسال طلبك بنجاح.']) {
                                    successMessage = formElrakami.translations['تم إرسال طلبك بنجاح.'];
                                }
                                // إذا كانت الرسالة تحتوي على النص العربي، استبدله بالترجمة
                                else if (successMessage.indexOf('تم إرسال طلبك بنجاح.') !== -1 && formElrakami.translations['تم إرسال طلبك بنجاح.']) {
                                    successMessage = successMessage.replace('تم إرسال طلبك بنجاح.', formElrakami.translations['تم إرسال طلبك بنجاح.']);
                                }
                            }

                            $message.addClass('success').html(successMessage);

                            // Clear form if successful
                            $form.find('input:not([type=hidden]), textarea, select').val('');
                            $form.find('input[type=checkbox], input[type=radio]').prop('checked', false);

                            // إعادة تعيين حالة السلة والطلبات المتروكة
                            resetCartState();

                            // Redirect if URL provided
                            if (response.data.redirect) {
                                setTimeout(function() {
                                    window.location.href = response.data.redirect;
                                }, 1000);
                            }
                        } else {
                            var errorMessage = '';

                            // Handle different error formats
                            if (typeof response.data === 'string') {
                                errorMessage = response.data;

                                // التحقق من رسالة تقييد الطلبات
                                var orderLimitText1 = 'عذراً، يمكنك إرسال طلب جديد بعد';
                                var orderLimitText2 = 'عذراً، لقد وصلت للحد الأقصى من الطلبات المسموح بها';

                                // استخدام الترجمة إذا كانت متوفرة
                                if (typeof formElrakami !== 'undefined' && formElrakami.translations) {
                                    if (formElrakami.translations[orderLimitText1]) {
                                        orderLimitText1 = formElrakami.translations[orderLimitText1];
                                    }
                                    if (formElrakami.translations[orderLimitText2]) {
                                        orderLimitText2 = formElrakami.translations[orderLimitText2];
                                    }
                                }

                                // ترجمة الرسالة بالكامل إذا كانت تحتوي على نص تقييد الطلبات
                                if (errorMessage.indexOf('عذراً، يمكنك إرسال طلب جديد بعد') !== -1 ||
                                    errorMessage.indexOf('عذراً، لقد وصلت للحد الأقصى من الطلبات المسموح بها') !== -1) {

                                    // ترجمة الرسالة بالكامل
                                    if (typeof formElrakami !== 'undefined' && formElrakami.translations) {
                                        // استبدال النص العربي بالترجمة
                                        if (formElrakami.translations['عذراً، يمكنك إرسال طلب جديد بعد']) {
                                            errorMessage = errorMessage.replace('عذراً، يمكنك إرسال طلب جديد بعد', formElrakami.translations['عذراً، يمكنك إرسال طلب جديد بعد']);
                                        }
                                        if (formElrakami.translations['عذراً، لقد وصلت للحد الأقصى من الطلبات المسموح بها']) {
                                            errorMessage = errorMessage.replace('عذراً، لقد وصلت للحد الأقصى من الطلبات المسموح بها', formElrakami.translations['عذراً، لقد وصلت للحد الأقصى من الطلبات المسموح بها']);
                                        }
                                    }
                                }

                                if (errorMessage.indexOf(orderLimitText1) !== -1 ||
                                    errorMessage.indexOf(orderLimitText2) !== -1 ||
                                    errorMessage.indexOf('عذراً، يمكنك إرسال طلب جديد بعد') !== -1 ||
                                    errorMessage.indexOf('عذراً، لقد وصلت للحد الأقصى من الطلبات المسموح بها') !== -1) {

                                    // استخراج النص من رسالة الخطأ
                                    var cleanMessage = errorMessage.replace('<div class="blocked-message">⛔ ', '').replace('</div>', '');

                                    // تحسين عرض الأرقام في الرسالة
                                    if (cleanMessage.indexOf('الحد الأقصى من الطلبات المسموح بها') !== -1) {
                                        // استخراج رقم الحد الأقصى للطلبات
                                        var maxOrdersMatch = cleanMessage.match(/\((\d+)\)/);
                                        if (maxOrdersMatch && maxOrdersMatch[1]) {
                                            var maxOrders = maxOrdersMatch[1];
                                            cleanMessage = cleanMessage.replace('(' + maxOrders + ')', '(<strong>' + maxOrders + '</strong>)');
                                        }
                                    }

                                    // إضافة أيقونة وتنسيق خاص لرسالة تقييد الطلبات
                                    errorMessage = '<div class="order-limit-error">' +
                                                   '<i class="fas fa-clock"></i> ' +
                                                   cleanMessage +
                                                   '</div>';
                                }
                            } else if (typeof response.data === 'object') {
                                if (response.data.message) {
                                    errorMessage = response.data.message;
                                } else if (response.data.error) {
                                    errorMessage = response.data.error;
                                } else {
                                    errorMessage = 'حدث خطأ غير معروف. يرجى المحاولة مرة أخرى.';
                                }

                                // Handle field-specific errors
                                if (response.data.field_errors) {
                                    $.each(response.data.field_errors, function(field, error) {
                                        $('#error-' + field).html(error);
                                    });
                                }
                            } else {
                                errorMessage = 'حدث خطأ غير معروف. يرجى المحاولة مرة أخرى.';
                            }

                            $message.addClass('error').html(errorMessage);

                            // تسجيل رسالة الخطأ للتصحيح
                            console.error('رسالة الخطأ المعروضة للمستخدم:', errorMessage);
                        }
                    },
                    error: function(xhr, status, error) {
                        $loading.hide();
                        console.error('خطأ في الاتصال:', status, error);

                        // تسجيل تفاصيل الخطأ للتصحيح
                        console.error('تفاصيل الخطأ:', {
                            status: xhr.status,
                            statusText: xhr.statusText,
                            responseText: xhr.responseText
                        });

                        // التحقق من أخطاء الأمان وتجديد رمز الأمان
                        if (xhr.status === 403 || (xhr.responseText &&
                            (xhr.responseText.includes('رمز الأمان') || xhr.responseText.includes('nonce')))) {

                            console.log('تم اكتشاف خطأ في رمز الأمان، محاولة التجديد...');

                            // محاولة تجديد رمز الأمان وإعادة الإرسال
                            refreshNonceAndRetry($form, function(success) {
                                if (!success) {
                                    $message.addClass('error').html('رمز الأمان غير صحيح. يرجى تحديث الصفحة والمحاولة مرة أخرى.');
                                }
                            });
                            return;
                        }

                        // رسالة خطأ مخصصة حسب نوع الخطأ
                        var errorMessage = 'حدث خطأ في الاتصال. يرجى المحاولة مرة أخرى.';

                        if (status === 'timeout') {
                            errorMessage = 'استغرق الطلب وقتًا طويلاً. يرجى التحقق من اتصالك بالإنترنت والمحاولة مرة أخرى.';
                        } else if (status === 'parsererror') {
                            errorMessage = 'حدث خطأ أثناء معالجة الاستجابة. يرجى تحديث الصفحة والمحاولة مرة أخرى.';
                        } else if (status === 'abort') {
                            errorMessage = 'تم إلغاء الطلب. يرجى المحاولة مرة أخرى.';
                        } else if (xhr.status === 0) {
                            errorMessage = 'لا يمكن الاتصال بالخادم. يرجى التحقق من اتصالك بالإنترنت.';
                        } else if (xhr.status === 404) {
                            errorMessage = 'الصفحة المطلوبة غير موجودة. يرجى تحديث الصفحة والمحاولة مرة أخرى.';
                        } else if (xhr.status === 500) {
                            errorMessage = 'حدث خطأ في الخادم. يرجى المحاولة مرة أخرى لاحقًا.';
                        }

                        // محاولة تحليل الاستجابة إذا كانت JSON
                        try {
                            var jsonResponse = JSON.parse(xhr.responseText);

                            if (jsonResponse && jsonResponse.data) {
                                errorMessage = jsonResponse.data;
                            }
                        } catch (e) {
                            console.error('خطأ في تحليل استجابة الخادم:', e);

                            // إذا كانت الاستجابة تحتوي على HTML، استخرج النص فقط
                            if (xhr.responseText && xhr.responseText.length > 0) {
                                var tempDiv = document.createElement('div');
                                tempDiv.innerHTML = xhr.responseText;
                                var textContent = tempDiv.textContent || tempDiv.innerText || '';

                                if (textContent.length > 0 && textContent.length < 500) {
                                    errorMessage = 'خطأ من الخادم: ' + textContent.substring(0, 200);
                                }
                            }
                        }

                        $message.addClass('error').html(errorMessage);
                    },
                    // إضافة معالجة قبل الإرسال
                    beforeSend: function() {
                        // إظهار رسالة التحميل
                        $message.removeClass('error success').empty();
                        $loading.show();
                    }
                });
            });

            // Clear field errors on input and trigger draft save
            $form.find('input, textarea, select').on('input change', function() {
                var fieldId = $(this).attr('id');
                $('#error-' + fieldId).empty();
                $message.empty();

                // تحديد أن هناك تغييرات غير محفوظة
                hasUnsavedChanges = true;

                // إذا كان الحقل مهمًا (مثل الاسم أو الهاتف)، نبدأ بحفظ المسودة
                var isImportantField = $(this).attr('name') === 'full_name' ||
                                      $(this).attr('name') === 'phone' ||
                                      $(this).attr('name') === 'address' ||
                                      $(this).attr('name') === 'state' ||
                                      $(this).attr('name') === 'municipality';

                // الحصول على وضع حفظ الطلب المتروك من الإعدادات
                var saveMode = (typeof formElrakami !== 'undefined' && formElrakami.abandoned_order_save_mode)
                              ? formElrakami.abandoned_order_save_mode
                              : 'smart';

                if (isImportantField) {
                    if (saveMode === 'on_exit') {
                        // لا نحفظ إلا عند المغادرة
                        return;
                    } else if (saveMode === 'immediate' || !cartButtonClicked) {
                        // حفظ فوري أو لم يتم النقر على زر السلة
                        scheduleFormDraftSave($form);
                    } else if (saveMode === 'smart' && cartButtonClicked) {
                        // وضع ذكي مع النقر على زر السلة - أخر الحفظ
                        scheduleAbandonedOrderSave($form);
                    }
                } else {
                    // زيادة عداد التغييرات للحقول غير المهمة
                    fieldChangeCount++;

                    // إذا وصل عدد التغييرات إلى 3، نبدأ بحفظ المسودة
                    if (fieldChangeCount >= 3) {
                        if (saveMode === 'on_exit') {
                            // لا نحفظ إلا عند المغادرة
                            fieldChangeCount = 0;
                            return;
                        } else if (saveMode === 'immediate' || !cartButtonClicked) {
                            scheduleFormDraftSave($form);
                        } else if (saveMode === 'smart' && cartButtonClicked) {
                            scheduleAbandonedOrderSave($form);
                        }
                        fieldChangeCount = 0; // إعادة ضبط العداد
                    }
                }
            });
        });
    }

    /**
     * Validate form fields
     *
     * @param {jQuery} $form Form element
     * @return {boolean} Valid or not
     */
    function validateForm($form) {
        var isValid = true;
        var firstErrorField = null;

        // Clear previous errors
        $form.find('.field-error').empty();

        // Reset field styles
        $form.find('input, textarea, select').removeClass('error');

        // Check all required fields by [required] attribute
        $form.find('input[required], textarea[required], select[required]').each(function() {
            var $input = $(this);
            var fieldId = $input.attr('id');
            var errorId = 'error-' + fieldId;
            var $errorDisplay = $('#' + errorId);
            var value = $input.val().trim();

            // If error container doesn't exist, create it
            if ($errorDisplay.length === 0) {
                var $parent = $input.closest('.form-group, .input-group').parent();
                $parent.append('<div class="field-error" id="' + errorId + '"></div>');
                $errorDisplay = $('#' + errorId);
            }

            // Special handling for different field types
            switch ($input.attr('type')) {
                case 'checkbox':
                case 'radio':
                    if (!$input.is(':checked') && $form.find('input[name="' + $input.attr('name') + '"]:checked').length === 0) {
                        $errorDisplay.html(formElrakami.translations && formElrakami.translations.field_required || 'هذا الحقل مطلوب');
                        $input.addClass('error');
                        isValid = false;
                        if (firstErrorField === null) {
                            firstErrorField = $input;
                        }
                    }
                    break;

                default:
                    if (!value) {
                        $errorDisplay.html(formElrakami.translations && formElrakami.translations.field_required || 'هذا الحقل مطلوب');
                        $input.addClass('error');
                        isValid = false;
                        if (firstErrorField === null) {
                            firstErrorField = $input;
                        }
                    }
                    break;
            }
        });

        // Validate email fields
        $form.find('input[type="email"]').each(function() {
            var $input = $(this);
            var value = $input.val().trim();

            if (value && !isValidEmail(value)) {
                var fieldId = $input.attr('id');
                $('#error-' + fieldId).html(formElrakami.translations && formElrakami.translations.invalid_email || 'البريد الإلكتروني غير صالح');
                $input.addClass('error');
                isValid = false;
                if (firstErrorField === null) {
                    firstErrorField = $input;
                }
            }
        });

        // Validate phone fields
        $form.find('input[type="tel"]').each(function() {
            var $input = $(this);
            var value = $input.val().trim();

            if (value) {
                // التحقق مما إذا كان التحقق من رقم الهاتف مفعل
                if (formElrakami.phone_validation_enabled === '1') {
                    // Remove any non-numeric characters except + for validation
                    var digitsOnly = value.replace(/[^\d+]/g, '');

                    // التحقق مما إذا كان تخصيص التحقق من رقم الهاتف مفعل
                    if (formElrakami.custom_phone_validation === '1') {
                        // الحصول على البادئات المسموح بها وطول رقم الهاتف من الإعدادات
                        var allowedPrefixes = formElrakami.phone_prefixes.split(',');
                        var phoneLength = parseInt(formElrakami.phone_length);

                        // التحقق من طول رقم الهاتف
                        if (digitsOnly.length !== phoneLength &&
                            !(digitsOnly.startsWith('+') && digitsOnly.length === phoneLength + 4)) { // +213 format
                            var fieldId = $input.attr('id');
                            $('#error-' + fieldId).html('رقم الهاتف غير صحيح. يجب أن يكون ' + phoneLength + ' أرقام');
                            $input.addClass('error');
                            isValid = false;
                            if (firstErrorField === null) {
                                firstErrorField = $input;
                            }
                        }
                        // التحقق من البادئة
                        else {
                            var hasValidPrefix = false;
                            for (var i = 0; i < allowedPrefixes.length; i++) {
                                var prefix = allowedPrefixes[i].trim();
                                if (digitsOnly.startsWith(prefix) ||
                                    (digitsOnly.startsWith('+213') && digitsOnly.substring(4).startsWith(prefix.substring(1)))) {
                                    hasValidPrefix = true;
                                    break;
                                }
                            }

                            if (!hasValidPrefix) {
                                var fieldId = $input.attr('id');
                                $('#error-' + fieldId).html('رقم الهاتف غير صحيح. يجب أن يبدأ بإحدى البادئات: ' + formElrakami.phone_prefixes);
                                $input.addClass('error');
                                isValid = false;
                                if (firstErrorField === null) {
                                    firstErrorField = $input;
                                }
                            }
                        }
                    } else {
                        // استخدام التحقق الافتراضي
                        // Algeria phone numbers can be 10 digits (0X XX XX XX XX)
                        // or international format +213 X XX XX XX XX
                        if (!/^(\+?213|0)[5-7]\d{8}$/.test(digitsOnly)) {
                            var fieldId = $input.attr('id');
                            $('#error-' + fieldId).html('رقم الهاتف غير صحيح. يجب أن يكون رقم جزائري صالح');
                            $input.addClass('error');
                            isValid = false;
                            if (firstErrorField === null) {
                                firstErrorField = $input;
                            }
                        }
                    }
                }
            }
        });

        // Check state and municipality fields
        var $stateField = $form.find('select[name="state"]');
        var $municipalityField = $form.find('select[name="municipality"]');
        var $addressField = $form.find('input[name="address"]');

        if ($stateField.length && $stateField.prop('required') && !$stateField.val()) {
            $('#error-state').html('يرجى اختيار الولاية');
            $stateField.addClass('error');
            isValid = false;
            if (firstErrorField === null) {
                firstErrorField = $stateField;
            }
        }

        if ($municipalityField.length && $municipalityField.prop('required') && !$municipalityField.val()) {
            $('#error-municipality').html('يرجى اختيار البلدية');
            $municipalityField.addClass('error');
            isValid = false;
            if (firstErrorField === null) {
                firstErrorField = $municipalityField;
            }
        }

        if ($addressField.length && $addressField.prop('required') && !$addressField.val().trim()) {
            $('#error-address').html('يرجى إدخال العنوان التفصيلي');
            $addressField.addClass('error');
            isValid = false;
            if (firstErrorField === null) {
                firstErrorField = $addressField;
            }
        }

        // Ensure a shipping method is selected if shipping methods exist and are visible
        var $shippingMethods = $form.find('input[name="shipping_method_option"]');
        var $shippingContainer = $form.find('.shipping-methods-container');

        // التحقق من طرق التوصيل فقط إذا كانت مرئية ومفعلة
        if ($shippingMethods.length > 0 &&
            $shippingContainer.is(':visible') &&
            !$form.find('input[name="shipping_method_option"]:checked').length) {

            if (!$shippingContainer.find('.field-error').length) {
                $shippingContainer.append('<div class="field-error">يرجى اختيار طريقة التوصيل</div>');
            } else {
                $shippingContainer.find('.field-error').html('يرجى اختيار طريقة التوصيل');
            }
            isValid = false;
            if (firstErrorField === null) {
                firstErrorField = $shippingMethods.first();
            }
        }

        // إذا كان هناك حقل غير صالح، قم بالتمرير إليه
        if (!isValid && firstErrorField !== null) {
            // التمرير إلى الحقل الأول الذي يحتوي على خطأ
            $('html, body').animate({
                scrollTop: firstErrorField.offset().top - 100
            }, 300, 'linear');

            // تركيز الحقل
            setTimeout(function() {
                firstErrorField.focus();
            }, 350);


        }

        return isValid;
    }

    /**
     * Check if email is valid
     *
     * @param {string} email Email to validate
     * @return {boolean} Valid or not
     */
    function isValidEmail(email) {
        var re = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
        return re.test(String(email).toLowerCase());
    }

    /**
     * Initialize state and shipping fields functionality
     */
    function initStateAndShippingFields() {
        // التحقق من طرق الشحن المفعلة وإخفاء القسم إذا كانت هناك طريقة واحدة فقط
        checkShippingMethodsVisibility();

        // Find forms with state, municipality, and shipping fields
        $forms.each(function() {
            var $form = $(this);
            var productId = $form.data('product-id') || $form.find('input[name="product_id"]').val() || 0;
            var $stateField = $form.find('select[name="state"]');
            var $municipalityField = $form.find('select[name="municipality"]');
            var $shippingMethodsContainer = $form.find('.shipping-methods-container');
            var $shippingCostField = $form.find('input[name="shipping_cost"]');
            var $shippingMethodField = $form.find('input[name="shipping_method"]');
            var $totalPriceDisplay = $form.find('.total-price-display');

            // عرض طرق الشحن الافتراضية إذا كان حقل الولاية غير موجود، وإلا عرض رسالة لاختيار الولاية
            if ($shippingMethodsContainer.length > 0 && $shippingMethodsContainer.is(':empty')) {
                if ($stateField.length === 0 || $stateField.closest('.form-group').css('display') === 'none') {
                    // إذا كان حقل الولاية غير موجود أو مخفي، أظهر طرق الشحن الافتراضية
                    var html = '<div class="shipping-methods-list">';

                    // استخدام بيانات طرق الشحن من إعدادات النموذج
                    var defaultCost1 = parseFloat(formElrakami.default_shipping_1_cost);
                    var defaultCost2 = parseFloat(formElrakami.default_shipping_2_cost);
                    var shipping1Enabled = parseInt(formElrakami.default_shipping_1_enabled) === 1;
                    var shipping2Enabled = parseInt(formElrakami.default_shipping_2_enabled) === 1;

                    // تحضير مصفوفة لطرق الشحن المفعلة
                    var enabledMethods = [];

                    // إضافة طريقة الشحن الأولى إذا كانت مفعلة
                    if (shipping1Enabled) {
                        enabledMethods.push({
                            value: 'standard_shipping',
                            cost: defaultCost1,
                            title: formElrakami.default_shipping_1_title,
                            description: formElrakami.default_shipping_1_description
                        });
                    }

                    // إضافة طريقة الشحن الثانية إذا كانت مفعلة
                    if (shipping2Enabled) {
                        enabledMethods.push({
                            value: 'economy_shipping',
                            cost: defaultCost2,
                            title: formElrakami.default_shipping_2_title,
                            description: formElrakami.default_shipping_2_description
                        });
                    }

                    // إذا لم تكن هناك طرق شحن مفعلة، إضافة طريقة افتراضية
                    if (enabledMethods.length === 0) {
                        enabledMethods.push({
                            value: 'free_shipping',
                            cost: 0,
                            title: formElrakami.translations && formElrakami.translations.standard_shipping || 'توصيل قياسي',
                            description: formElrakami.translations && formElrakami.translations.free_shipping || 'توصيل مجاني'
                        });
                    }

                    // التحقق مما إذا كانت هناك طريقة شحن واحدة فقط
                    if (enabledMethods.length === 1) {
                        // إذا كانت هناك طريقة واحدة فقط، نختارها تلقائيًا ونخفي قسم طرق التوصيل
                        var method = enabledMethods[0];



                        // إنشاء حقل مخفي لطريقة الشحن
                        $form.find('input[name="shipping_method_option"]').remove();
                        $form.append('<input type="hidden" name="shipping_method_option" value="' + method.value + '" data-cost="' + method.cost + '">');

                        // تحديث حقول الشحن
                        $shippingCostField.val(method.cost);
                        $shippingMethodField.val(method.value);

                        // حفظ اسم طريقة الشحن في حقل مخفي
                        if ($form.find('input[name="shipping_method_name"]').length === 0) {
                            $form.append('<input type="hidden" name="shipping_method_name" value="' + method.title + '">');
                        } else {
                            $form.find('input[name="shipping_method_name"]').val(method.title);
                        }

                        // إخفاء قسم طرق التوصيل بالكامل
                        var $shippingSection = $shippingMethodsContainer.closest('.pexlat-form-address-fields');
                        $shippingSection.hide();

                        // تحديث السعر الإجمالي
                        updateTotalPrice($form);

                        return; // الخروج من الدالة
                    }

                    // إذا كان هناك أكثر من طريقة شحن، نعرضها
                    for (var i = 0; i < enabledMethods.length; i++) {
                        var method = enabledMethods[i];
                        html += '<div class="shipping-method-option">';
                        html += '<label>';
                        html += '<input type="radio" name="shipping_method_option" value="' + method.value + '" data-cost="' + method.cost + '">';
                        html += '<div class="shipping-method-details">';
                        html += '<div class="shipping-method-title">' + method.title + '</div>';
                        if (method.description) {
                            html += '<div class="shipping-method-description">' + method.description + '</div>';
                        }
                        html += '<div class="shipping-method-price">' + formatPrice(method.cost) + '</div>';
                        html += '</div>';
                        html += '</label>';
                        html += '</div>';
                    }

                    html += '</div>';
                    $shippingMethodsContainer.html(html);

                    // تحديد أول طريقة توصيل تلقائيًا
                    $shippingMethodsContainer.find('input[name="shipping_method_option"]:first').prop('checked', true).trigger('change');

                    // إضافة طرق التوصيل المبسطة إلى ملخص الطلب إذا كان العرض المبسط مفعل
                    updateSimpleShippingMethods($form, enabledMethods);
                } else {
                    // عرض رسالة تطلب اختيار الولاية
                    var pleaseChooseStateText = formElrakami.translations && formElrakami.translations.please_choose_state || 'الرجاء اختيار الولاية لعرض طرق التوصيل المتاحة';
                    $shippingMethodsContainer.html(
                        '<div class="shipping-methods-empty">' +
                            '<i class="fas fa-map-marker-alt"></i>' +
                            '<p>' + pleaseChooseStateText + '</p>' +
                        '</div>'
                    );
                }
            }

            // تحسين تجربة المستخدم مع القوائم المنسدلة العادية
            if ($stateField.length > 0) {
                // تطبيق النمط على القائمة المنسدلة العادية
                $stateField.css({
                    'width': '100%',
                    'height': '100%'
                });

                // تحديث عرض سعر الشحن ليظهر "اختر الولاية" عند تهيئة النموذج
                if (!$stateField.val() || $stateField.val() === '') {
                    var $shippingPriceDisplay = $form.find('.shipping-price-display');
                    if ($shippingPriceDisplay.length > 0) {
                        var chooseStateText = 'اختر الولاية';
                        // استخدام الترجمة إذا كانت متوفرة
                        if (typeof formElrakami !== 'undefined' && formElrakami.translations && formElrakami.translations['اختر الولاية']) {
                            chooseStateText = formElrakami.translations['اختر الولاية'];
                        }
                        $shippingPriceDisplay.html('<span class="select-state-message">' + chooseStateText + '</span>');
                    }
                }
            }

            if ($municipalityField.length > 0) {
                // تطبيق النمط على القائمة المنسدلة العادية
                $municipalityField.css({
                    'width': '100%',
                    'height': '100%'
                });
            }

            // If state field exists, initialize state loading
            if ($stateField.length > 0) {
                // Load states for default country (DZ)
                loadStates('DZ', $stateField);

                // When state field changes, load municipalities and reset shipping methods
                $stateField.on('change', function() {
                    var state = $(this).val();

                    if (state) {
                        // Update shipping container with loading message
                        $shippingMethodsContainer.html(
                            '<div class="shipping-methods-loading">' +
                                '<i class="fas fa-spinner fa-spin"></i>' +
                                '<p>جاري تحميل طرق التوصيل...</p>' +
                            '</div>'
                        );

                        // Load municipalities for selected state
                        loadCities('DZ', state, $municipalityField);

                        // مباشرة بعد اختيار الولاية، قم بتحميل طرق التوصيل
                        // استخدم قيمة فارغة للبلدية لإظهار جميع طرق التوصيل المتاحة للولاية
                        loadShippingMethods('DZ', state, '', productId, $shippingMethodsContainer);

                        // Reset shipping methods if municipality is selected
                        if ($municipalityField.val()) {
                            $municipalityField.val('');
                        }
                    } else {
                        // Reset municipality field
                        resetField($municipalityField);

                        // Clear shipping methods
                        var pleaseChooseStateText = formElrakami.translations && formElrakami.translations.please_choose_state || 'الرجاء اختيار الولاية لعرض طرق التوصيل المتاحة';
                        $shippingMethodsContainer.html(
                            '<div class="shipping-methods-empty">' +
                                '<i class="fas fa-map-marker-alt"></i>' +
                                '<p>' + pleaseChooseStateText + '</p>' +
                            '</div>'
                        );

                        // Reset shipping cost
                        $shippingCostField.val(0);
                        $shippingMethodField.val('');

                        // تحديث عرض سعر الشحن ليظهر "اختر الولاية"
                        var $shippingPriceDisplay = $form.find('.shipping-price-display');
                        if ($shippingPriceDisplay.length > 0) {
                            var chooseStateText = 'اختر الولاية';
                            // استخدام الترجمة إذا كانت متوفرة
                            if (typeof formElrakami !== 'undefined' && formElrakami.translations && formElrakami.translations['اختر الولاية']) {
                                chooseStateText = formElrakami.translations['اختر الولاية'];
                            }
                            $shippingPriceDisplay.html('<span class="select-state-message">' + chooseStateText + '</span>');
                        }

                        // Update total price
                        updateTotalPrice($form);
                    }
                });
            }

            // When municipality field changes, load shipping methods
            if ($municipalityField.length > 0) {
                $municipalityField.on('change', function() {
                    var municipality = $(this).val();
                    var state = $stateField.val();

                    if (municipality && state) {
                        // Update shipping container with loading message
                        $shippingMethodsContainer.html(
                            '<div class="shipping-methods-loading">' +
                                '<i class="fas fa-spinner fa-spin"></i>' +
                                '<p>' + (formElrakami.translations && formElrakami.translations.loading_shipping_methods || 'جاري تحميل طرق التوصيل...') + '</p>' +
                            '</div>'
                        );

                        // Load shipping methods for selected location
                        loadShippingMethods('DZ', state, municipality, productId, $shippingMethodsContainer);
                    } else if (state) {
                        // إذا لم يتم اختيار البلدية ولكن تم اختيار الولاية، قم بتحميل طرق التوصيل للولاية
                        loadShippingMethods('DZ', state, '', productId, $shippingMethodsContainer);

                        // Reset shipping cost
                        $shippingCostField.val(0);
                        $shippingMethodField.val('');

                        // Update total price
                        updateTotalPrice($form);
                    }
                });
            }

            // When shipping method is selected, update shipping cost and total
            $form.on('change', 'input[name="shipping_method_option"]', function() {
                var shippingMethod = $(this).val();
                var shippingCost = $(this).data('cost');

                // إزالة الفئة المحددة من جميع الخيارات
                $('.shipping-method-option').removeClass('selected');

                // إضافة الفئة المحددة للخيار المختار
                $(this).closest('.shipping-method-option').addClass('selected');

                // الحصول على اسم طريقة الشحن من عنوان الطريقة
                var shippingMethodName = $(this).closest('.shipping-method-option').find('.shipping-method-title').text();

                // تسجيل للتصحيح
                console.log('تم تغيير طريقة الشحن:');
                console.log('shippingMethod:', shippingMethod);
                console.log('shippingCost:', shippingCost);
                console.log('shippingMethodName:', shippingMethodName);

                // Update hidden fields
                $shippingCostField.val(shippingCost);
                $shippingMethodField.val(shippingMethod);

                // التأكد من تحديث القيم
                console.log('القيم بعد التحديث:');
                console.log('shipping_cost field value:', $shippingCostField.val());
                console.log('shipping_method field value:', $shippingMethodField.val());

                // حفظ اسم طريقة الشحن في حقل مخفي
                if ($form.find('input[name="shipping_method_name"]').length === 0) {
                    $form.append('<input type="hidden" name="shipping_method_name" value="' + shippingMethodName + '">');
                } else {
                    $form.find('input[name="shipping_method_name"]').val(shippingMethodName);
                }

                // Update total price
                updateTotalPrice($form);

                // حفظ المسودة بعد اختيار طريقة الشحن
                scheduleFormDraftSave($form);
            });
        });
    }

    /**
     * Load states for a country
     *
     * @param {string} country Country code
     * @param {jQuery} $stateField State select field
     */
    function loadStates(country, $stateField) {
        // إعادة تهيئة الحقل
        $stateField.find('option:not(:first)').remove();

        // Send AJAX request to load states
        $.ajax({
            url: formElrakami.ajaxurl,
            type: 'POST',
            data: {
                action: 'pexlat_form_get_states',
                country: country,
                nonce: formElrakami.nonce
            },
            dataType: 'json',
            success: function(response) {
                if (response.success && response.data) {
                    // إضافة الولايات بالترتيب الصحيح من 01 إلى 58
                    for (var i = 1; i <= 58; i++) {
                        var key = (i < 10 ? '0' : '') + i;
                        if (response.data[key]) {
                            $stateField.append($('<option></option>').val(key).text(response.data[key]));
                        }
                    }

                    // If there's a value from a previous save, restore it
                    if ($stateField.data('saved-value')) {
                        var savedValue = $stateField.data('saved-value');
                        $stateField.val(savedValue).trigger('change');
                    }
                }
            }
        });
    }

    /**
     * Load municipalities for a state/province
     *
     * @param {string} country Country code
     * @param {string} state State/province code
     * @param {jQuery} $municipalityField Municipality select field
     */
    function loadCities(country, state, $municipalityField) {
        // إعادة تهيئة الحقل
        $municipalityField.find('option:not(:first)').remove();

        // Send AJAX request to load municipalities
        $.ajax({
            url: formElrakami.ajaxurl,
            type: 'POST',
            data: {
                action: 'pexlat_form_get_cities',
                country: country,
                state: state,
                nonce: formElrakami.nonce
            },
            dataType: 'json',
            success: function(response) {
                if (response.success && response.data) {
                    // Add municipalities to select field
                    $.each(response.data, function(i, city) {
                        $municipalityField.append($('<option></option>').val(city).text(city));
                    });

                    // If there's a value from a previous save, restore it
                    if ($municipalityField.data('saved-value') && state === $municipalityField.data('saved-state')) {
                        var savedValue = $municipalityField.data('saved-value');
                        $municipalityField.val(savedValue).trigger('change');
                    }

                    // تطبيق النمط على القائمة المنسدلة العادية
                    $municipalityField.css({
                        'width': '100%',
                        'height': '100%'
                    });
                } else {
                    // If no municipalities found, show error
                    console.error('خطأ في تحميل البلديات:', response);

                    // Add a placeholder option
                    $municipalityField.append($('<option></option>').val('').text('لم يتم العثور على بلديات'));

                    // تطبيق النمط على القائمة المنسدلة حتى مع عدم وجود بيانات
                    $municipalityField.css({
                        'width': '100%',
                        'height': '100%'
                    });
                }
            },
            error: function() {
                console.error('خطأ في الاتصال بالخادم');

                // Add a placeholder option
                $municipalityField.append($('<option></option>').val('').text('حدث خطأ في تحميل البلديات'));

                // تطبيق النمط على القائمة المنسدلة حتى مع وجود خطأ
                $municipalityField.css({
                    'width': '100%',
                    'height': '100%'
                });
            }
        });
    }

    /**
     * Load shipping methods for a location
     *
     * @param {string} country Country code
     * @param {string} state State/province code
     * @param {string} municipality Municipality code
     * @param {int} productId Product ID
     * @param {jQuery} $container Container for shipping methods
     */
    function loadShippingMethods(country, state, municipality, productId, $container) {
        // الحصول على النموذج الذي يحتوي على حاوية طرق الشحن
        var $form = $container.closest('form');

        // Send AJAX request to load shipping methods
        $.ajax({
            url: formElrakami.ajaxurl,
            type: 'POST',
            data: {
                action: 'pexlat_form_get_shipping_methods',
                country: country,
                state: state,
                municipality: municipality,
                product_id: productId,
                nonce: formElrakami.nonce
            },
            dataType: 'json',
            success: function(response) {
                if (response.success && response.data) {
                    // التحقق مما إذا كانت هناك طريقة شحن واحدة فقط
                    if (response.data.length === 1) {
                        // إذا كانت هناك طريقة واحدة فقط، نختارها تلقائيًا ونخفي قسم طرق التوصيل
                        var method = response.data[0];



                        // إنشاء حقل مخفي لطريقة الشحن
                        $form.find('input[name="shipping_method_option"]').remove();
                        $form.append('<input type="hidden" name="shipping_method_option" value="' + method.id + '" data-cost="' + method.cost + '">');

                        // تحديث حقول الشحن
                        $form.find('input[name="shipping_cost"]').val(method.cost);
                        $form.find('input[name="shipping_method"]').val(method.id);

                        // حفظ اسم طريقة الشحن في حقل مخفي
                        if ($form.find('input[name="shipping_method_name"]').length === 0) {
                            $form.append('<input type="hidden" name="shipping_method_name" value="' + method.title + '">');
                        } else {
                            $form.find('input[name="shipping_method_name"]').val(method.title);
                        }

                        // إخفاء قسم طرق التوصيل بالكامل
                        var $shippingSection = $container.closest('.pexlat-form-address-fields');
                        $shippingSection.hide();


                        // تحديث السعر الإجمالي
                        updateTotalPrice($form);

                        return; // الخروج من الدالة
                    }

                    // إذا كان هناك أكثر من طريقة شحن، نعرضها
                    var html = '<div class="shipping-methods-list">';

                    if (response.data.length > 0) {
                        $.each(response.data, function(i, method) {
                            html += '<div class="shipping-method-option">';
                            html += '<label>';
                            html += '<input type="radio" name="shipping_method_option" value="' + method.id + '" data-cost="' + method.cost + '">';
                            html += '<div class="shipping-method-details">';
                            html += '<div class="shipping-method-title">' + method.title + '</div>';
                            html += '<div class="shipping-method-price">' + formatPrice(method.cost) + '</div>';

                            if (method.description) {
                                html += '<div class="shipping-method-description">' + method.description + '</div>';
                            }

                            html += '</div>';
                            html += '</label>';
                            html += '</div>';
                        });
                    } else {
                        // إضافة طرق الشحن الافتراضية عندما لا تتوفر طرق شحن من الخادم
                        // استخدام بيانات طرق الشحن من إعدادات النموذج
                        var defaultCost1 = parseFloat(formElrakami.default_shipping_1_cost);
                        var defaultCost2 = parseFloat(formElrakami.default_shipping_2_cost);
                        var shipping1Enabled = parseInt(formElrakami.default_shipping_1_enabled) === 1;
                        var shipping2Enabled = parseInt(formElrakami.default_shipping_2_enabled) === 1;

                        // تحضير مصفوفة لطرق الشحن المفعلة
                        var enabledMethods = [];

                        // إضافة طريقة الشحن الأولى إذا كانت مفعلة
                        if (shipping1Enabled) {
                            enabledMethods.push({
                                value: 'standard_shipping',
                                cost: defaultCost1,
                                title: formElrakami.default_shipping_1_title,
                                description: formElrakami.default_shipping_1_description
                            });
                        }

                        // إضافة طريقة الشحن الثانية إذا كانت مفعلة
                        if (shipping2Enabled) {
                            enabledMethods.push({
                                value: 'economy_shipping',
                                cost: defaultCost2,
                                title: formElrakami.default_shipping_2_title,
                                description: formElrakami.default_shipping_2_description
                            });
                        }

                        // إذا لم تكن هناك طرق شحن مفعلة، إضافة طريقة افتراضية
                        if (enabledMethods.length === 0) {
                            enabledMethods.push({
                                value: 'free_shipping',
                                cost: 0,
                                title: formElrakami.translations && formElrakami.translations.standard_shipping || 'توصيل قياسي',
                                description: formElrakami.translations && formElrakami.translations.free_shipping || 'توصيل مجاني'
                            });
                        }

                        // التحقق مما إذا كانت هناك طريقة شحن واحدة فقط
                        if (enabledMethods.length === 1) {
                            // إذا كانت هناك طريقة واحدة فقط، نختارها تلقائيًا ونخفي قسم طرق التوصيل
                            var method = enabledMethods[0];



                            // إنشاء حقل مخفي لطريقة الشحن
                            $form.find('input[name="shipping_method_option"]').remove();
                            $form.append('<input type="hidden" name="shipping_method_option" value="' + method.value + '" data-cost="' + method.cost + '">');

                            // تحديث حقول الشحن
                            $form.find('input[name="shipping_cost"]').val(method.cost);
                            $form.find('input[name="shipping_method"]').val(method.value);

                            // حفظ اسم طريقة الشحن في حقل مخفي
                            if ($form.find('input[name="shipping_method_name"]').length === 0) {
                                $form.append('<input type="hidden" name="shipping_method_name" value="' + method.title + '">');
                            } else {
                                $form.find('input[name="shipping_method_name"]').val(method.title);
                            }

                            // إخفاء قسم طرق التوصيل بالكامل
                            var $shippingSection = $container.closest('.pexlat-form-address-fields');
                            $shippingSection.hide();


                            // تحديث السعر الإجمالي
                            updateTotalPrice($form);

                            return; // الخروج من الدالة
                        }

                        // إذا كان هناك أكثر من طريقة شحن، نعرضها
                        for (var i = 0; i < enabledMethods.length; i++) {
                            var method = enabledMethods[i];
                            html += '<div class="shipping-method-option">';
                            html += '<label>';
                            html += '<input type="radio" name="shipping_method_option" value="' + method.value + '" data-cost="' + method.cost + '">';
                            html += '<div class="shipping-method-details">';
                            html += '<div class="shipping-method-title">' + method.title + '</div>';
                            if (method.description) {
                                html += '<div class="shipping-method-description">' + method.description + '</div>';
                            }
                            html += '<div class="shipping-method-price">' + formatPrice(method.cost) + '</div>';
                            html += '</div>';
                            html += '</label>';
                            html += '</div>';
                        }
                    }

                    html += '</div>';

                    // Add HTML to container
                    $container.html(html);

                    // إظهار قسم طرق التوصيل
                    $container.closest('.pexlat-form-address-fields').show();

                    // تحديد أول طريقة توصيل تلقائيًا
                    $container.find('input[name="shipping_method_option"]:first').prop('checked', true).trigger('change');

                    // إضافة طرق التوصيل المبسطة إلى ملخص الطلب إذا كان العرض المبسط مفعل
                    updateSimpleShippingMethods($form, response.data);
                } else {
                    // استخدام بيانات طرق الشحن من إعدادات النموذج
                    var defaultCost1 = parseFloat(formElrakami.default_shipping_1_cost);
                    var defaultCost2 = parseFloat(formElrakami.default_shipping_2_cost);
                    var shipping1Enabled = parseInt(formElrakami.default_shipping_1_enabled) === 1;
                    var shipping2Enabled = parseInt(formElrakami.default_shipping_2_enabled) === 1;

                    // تحضير مصفوفة لطرق الشحن المفعلة
                    var enabledMethods = [];

                    // إضافة طريقة الشحن الأولى إذا كانت مفعلة
                    if (shipping1Enabled) {
                        enabledMethods.push({
                            value: 'standard_shipping',
                            cost: defaultCost1,
                            title: formElrakami.default_shipping_1_title,
                            description: formElrakami.default_shipping_1_description
                        });
                    }

                    // إضافة طريقة الشحن الثانية إذا كانت مفعلة
                    if (shipping2Enabled) {
                        enabledMethods.push({
                            value: 'economy_shipping',
                            cost: defaultCost2,
                            title: formElrakami.default_shipping_2_title,
                            description: formElrakami.default_shipping_2_description
                        });
                    }

                    // إذا لم تكن هناك طرق شحن مفعلة، إضافة طريقة افتراضية
                    if (enabledMethods.length === 0) {
                        enabledMethods.push({
                            value: 'free_shipping',
                            cost: 0,
                            title: formElrakami.translations && formElrakami.translations.standard_shipping || 'توصيل قياسي',
                            description: formElrakami.translations && formElrakami.translations.free_shipping || 'توصيل مجاني'
                        });
                    }

                    // التحقق مما إذا كانت هناك طريقة شحن واحدة فقط
                    if (enabledMethods.length === 1) {
                        // إذا كانت هناك طريقة واحدة فقط، نختارها تلقائيًا ونخفي قسم طرق التوصيل
                        var method = enabledMethods[0];

                        // إنشاء حقل مخفي لطريقة الشحن
                        $form.find('input[name="shipping_method_option"]').remove();
                        $form.append('<input type="hidden" name="shipping_method_option" value="' + method.value + '" data-cost="' + method.cost + '">');

                        // تحديث حقول الشحن
                        $form.find('input[name="shipping_cost"]').val(method.cost);
                        $form.find('input[name="shipping_method"]').val(method.value);

                        // حفظ اسم طريقة الشحن في حقل مخفي
                        if ($form.find('input[name="shipping_method_name"]').length === 0) {
                            $form.append('<input type="hidden" name="shipping_method_name" value="' + method.title + '">');
                        } else {
                            $form.find('input[name="shipping_method_name"]').val(method.title);
                        }

                        // إخفاء قسم طرق التوصيل
                        $container.closest('.pexlat-form-address-fields').hide();

                        // تحديث السعر الإجمالي
                        updateTotalPrice($form);

                        return; // الخروج من الدالة
                    }

                    // إذا كان هناك أكثر من طريقة شحن، نعرضها
                    var html = '<div class="shipping-methods-list">';

                    for (var i = 0; i < enabledMethods.length; i++) {
                        var method = enabledMethods[i];
                        html += '<div class="shipping-method-option">';
                        html += '<label>';
                        html += '<input type="radio" name="shipping_method_option" value="' + method.value + '" data-cost="' + method.cost + '">';
                        html += '<div class="shipping-method-details">';
                        html += '<div class="shipping-method-title">' + method.title + '</div>';
                        if (method.description) {
                            html += '<div class="shipping-method-description">' + method.description + '</div>';
                        }
                        html += '<div class="shipping-method-price">' + formatPrice(method.cost) + '</div>';
                        html += '</div>';
                        html += '</label>';
                        html += '</div>';
                    }

                    html += '</div>';
                    $container.html(html);

                    // إظهار قسم طرق التوصيل
                    $container.closest('.pexlat-form-address-fields').show();

                    // تحديد أول طريقة توصيل تلقائيًا
                    $container.find('input[name="shipping_method_option"]:first').prop('checked', true).trigger('change');

                    // إضافة طرق التوصيل المبسطة إلى ملخص الطلب إذا كان العرض المبسط مفعل
                    updateSimpleShippingMethods($form, enabledMethods);
                }
            },
            error: function() {
                // حتى في حالة خطأ الاتصال، نعرض طرق الشحن الافتراضية
                // استخدام بيانات طرق الشحن من إعدادات النموذج
                var defaultCost1 = parseFloat(formElrakami.default_shipping_1_cost);
                var defaultCost2 = parseFloat(formElrakami.default_shipping_2_cost);
                var shipping1Enabled = parseInt(formElrakami.default_shipping_1_enabled) === 1;
                var shipping2Enabled = parseInt(formElrakami.default_shipping_2_enabled) === 1;

                // تحضير مصفوفة لطرق الشحن المفعلة
                var enabledMethods = [];

                // إضافة طريقة الشحن الأولى إذا كانت مفعلة
                if (shipping1Enabled) {
                    enabledMethods.push({
                        value: 'standard_shipping',
                        cost: defaultCost1,
                        title: formElrakami.default_shipping_1_title,
                        description: formElrakami.default_shipping_1_description
                    });
                }

                // إضافة طريقة الشحن الثانية إذا كانت مفعلة
                if (shipping2Enabled) {
                    enabledMethods.push({
                        value: 'economy_shipping',
                        cost: defaultCost2,
                        title: formElrakami.default_shipping_2_title,
                        description: formElrakami.default_shipping_2_description
                    });
                }

                // إذا لم تكن هناك طرق شحن مفعلة، إضافة طريقة افتراضية
                if (enabledMethods.length === 0) {
                    enabledMethods.push({
                        value: 'free_shipping',
                        cost: 0,
                        title: formElrakami.translations && formElrakami.translations.standard_shipping || 'توصيل قياسي',
                        description: formElrakami.translations && formElrakami.translations.free_shipping || 'توصيل مجاني'
                    });
                }

                // التحقق مما إذا كانت هناك طريقة شحن واحدة فقط
                if (enabledMethods.length === 1) {
                    // إذا كانت هناك طريقة واحدة فقط، نختارها تلقائيًا ونخفي قسم طرق التوصيل
                    var method = enabledMethods[0];



                    // إنشاء حقل مخفي لطريقة الشحن
                    $form.find('input[name="shipping_method_option"]').remove();
                    $form.append('<input type="hidden" name="shipping_method_option" value="' + method.value + '" data-cost="' + method.cost + '">');

                    // تحديث حقول الشحن
                    $form.find('input[name="shipping_cost"]').val(method.cost);
                    $form.find('input[name="shipping_method"]').val(method.value);

                    // حفظ اسم طريقة الشحن في حقل مخفي
                    if ($form.find('input[name="shipping_method_name"]').length === 0) {
                        $form.append('<input type="hidden" name="shipping_method_name" value="' + method.title + '">');
                    } else {
                        $form.find('input[name="shipping_method_name"]').val(method.title);
                    }

                    // إخفاء قسم طرق التوصيل بالكامل
                    var $shippingSection = $container.closest('.pexlat-form-address-fields');
                    $shippingSection.hide();


                    // تحديث السعر الإجمالي
                    updateTotalPrice($form);

                    return; // الخروج من الدالة
                }

                // إذا كان هناك أكثر من طريقة شحن، نعرضها
                var html = '<div class="shipping-methods-list">';

                for (var i = 0; i < enabledMethods.length; i++) {
                    var method = enabledMethods[i];
                    html += '<div class="shipping-method-option">';
                    html += '<label>';
                    html += '<input type="radio" name="shipping_method_option" value="' + method.value + '" data-cost="' + method.cost + '">';
                    html += '<div class="shipping-method-details">';
                    html += '<div class="shipping-method-title">' + method.title + '</div>';
                    if (method.description) {
                        html += '<div class="shipping-method-description">' + method.description + '</div>';
                    }
                    html += '<div class="shipping-method-price">' + formatPrice(method.cost) + '</div>';
                    html += '</div>';
                    html += '</label>';
                    html += '</div>';
                }

                html += '</div>';
                $container.html(html);

                // إظهار قسم طرق التوصيل
                $container.closest('.pexlat-form-address-fields').show();

                // تحديد أول طريقة توصيل تلقائيًا
                $container.find('input[name="shipping_method_option"]:first').prop('checked', true).trigger('change');
            }
        });
    }

    /**
     * Reset a field to its initial state
     *
     * @param {jQuery} $field Field to reset
     */
    function resetField($field) {
        $field.val('');

        // تطبيق النمط على القائمة المنسدلة
        $field.css({
            'width': '100%',
            'height': '100%'
        });

        // Remove options except the first one (placeholder)
        $field.find('option:not(:first)').remove();
    }

    /**
     * Update total price based on product price and shipping cost
     *
     * @param {jQuery} $form The form
     */
    function updateTotalPrice($form) {
       // Get price and shipping cost elements
       var $totalDisplay = $form.find('.total-price-display');
       var $productPriceDisplay = $form.find('.product-price-display');
       var $shippingPriceDisplay = $form.find('.shipping-price-display');
       var $quantityInput = $form.find('input[name="quantity"]');

       if ($totalDisplay.length > 0) {
           // Get base product price
           var basePrice = parseFloat($form.find('input[name="product_price"]').val()) || 0;

           // Get quantity
           var quantity = parseInt($quantityInput.val()) || 1;

           // تحديث عرض الكمية في ملخص الطلب
           $('.quantity-number').text(quantity);

           // Calculate product total
           var productPrice = basePrice * quantity;

           // Get shipping cost
           var shippingCost = parseFloat($form.find('input[name="shipping_cost"]').val()) || 0;

           // Calculate total
           var totalPrice = productPrice + shippingCost;

           // Update displays
           if ($productPriceDisplay.length > 0) {
               $productPriceDisplay.text(formatPrice(productPrice));
           }

           if ($shippingPriceDisplay.length > 0) {
               // التحقق من حالة اختيار الولاية
               var $stateField = $form.find('select[name="state"]');

               // التحقق مما إذا كان حقل الولاية موجودًا ومفعلًا
               if ($stateField.length > 0 && $stateField.is(':visible')) {
                   // التحقق مما إذا لم يتم اختيار قيمة للولاية
                   if (!$stateField.val() || $stateField.val() === '') {
                       // عرض "اختر الولاية" إذا لم يتم اختيار الولاية
                       $shippingPriceDisplay.html('<span class="select-state-message">اختر الولاية</span>');
                       return; // الخروج من الدالة لمنع تحديث سعر الشحن
                   }
               }

               // إذا تم اختيار الولاية أو كان حقل الولاية غير موجود
               if (shippingCost === 0) {
                   // عرض "توصيل مجاني" في بطاقة خضراء إذا كان سعر الشحن صفر
                   var freeShippingText = formElrakami.translations && formElrakami.translations.free_shipping || 'توصيل مجاني';
                   $shippingPriceDisplay.html('<span class="free-shipping-badge">' + freeShippingText + '</span>');
               } else {
                   // عرض سعر الشحن بالطريقة العادية
                   $shippingPriceDisplay.text(formatPrice(shippingCost));
               }
           }

           $totalDisplay.text(formatPrice(totalPrice));

           // تحديث السعر في زر الطلب إذا كان مفعلاً
           var $buttonTotalPrice = $form.find('.pexlat-form-submit .button-total-price .total-price-display');
           if ($buttonTotalPrice.length > 0) {
               $buttonTotalPrice.text(formatPrice(totalPrice));
           }

           // تحديث السعر في الزر المثبت إذا كان مفعلاً
           var $stickyButtonTotalPrice = $('.pexlat-form-sticky-bar-button .sticky-button-total-price .total-price-display');
           if ($stickyButtonTotalPrice.length > 0) {
               $stickyButtonTotalPrice.text(formatPrice(totalPrice));
           }
       }
    }

    /**
     * Initialize quantity controls for all forms
     */
    function initQuantityControls() {
        $forms.each(function() {
            initFormQuantityControls($(this));
        });
    }

    /**
     * Initialize quantity controls for a specific form
     * @param {jQuery} $form The form to initialize controls for
     */
    function initFormQuantityControls($form) {
        var $quantityInput = $form.find('input[name="quantity"]');
        var $minusBtn = $form.find('.quantity-btn.minus');
        var $plusBtn = $form.find('.quantity-btn.plus');
        var minQuantity = 1;
        var maxQuantity = 99;

        // تعطيل الأحداث القديمة إن وجدت
        $minusBtn.off('click');
        $plusBtn.off('click');
        $quantityInput.off('input change keypress');

        // ضمان قيمة أولية صحيحة
        var quantity = parseInt($quantityInput.val()) || 1;
        $quantityInput.val(quantity);

        // تحديث عرض الكمية في ملخص الطلب عند التهيئة
        $('.quantity-number').text(quantity);

        // دالة مساعدة لتحديث الكمية
        function updateQuantity(newQuantity) {
            if (newQuantity >= minQuantity && newQuantity <= maxQuantity) {

                quantity = newQuantity;
                $quantityInput.val(quantity);

                // تحديث عرض الكمية في ملخص الطلب
                $('.quantity-number').text(quantity);

                // تشغيل حدث تغيير الكمية لتحديث المتغير المناسب
                $quantityInput.trigger('change');

                updateTotalPrice($form);
                scheduleFormDraftSave($form);
            }
        }

        // معالجة نقر زر الإنقاص
        $minusBtn.on('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            if (quantity > minQuantity) {
                // الحصول على المتغير المحدد حاليًا
                var $selectedInput = $form.data('selected-variation-input');
                var currentRequiredQuantity = $selectedInput ? parseInt($selectedInput.data('required-quantity')) || 0 : 0;

                // إذا كان هناك متغير محدد وكانت الكمية الحالية تساوي الكمية المطلوبة للمتغير المحدد
                if ($selectedInput && quantity === currentRequiredQuantity) {


                    // جمع جميع المتغيرات مع كمياتها المطلوبة
                    var variationsWithQuantities = [];
                    $form.find('.variation-button-input').each(function() {
                        var $input = $(this);
                        var inputRequiredQuantity = parseInt($input.data('required-quantity')) || 0;
                        if (inputRequiredQuantity > 0) {
                            variationsWithQuantities.push({
                                input: $input,
                                quantity: inputRequiredQuantity
                            });
                        }
                    });

                    // ترتيب المتغيرات حسب الكمية (من الأصغر إلى الأكبر)
                    variationsWithQuantities.sort(function(a, b) {
                        return a.quantity - b.quantity;
                    });

                    // البحث عن المتغير السابق (الأقل كمية مطلوبة)
                    var previousVariation = null;
                    for (var i = 0; i < variationsWithQuantities.length; i++) {
                        var variation = variationsWithQuantities[i];
                        if (variation.quantity === currentRequiredQuantity) {
                            // إذا وجدنا المتغير الحالي، نتحقق مما إذا كان هناك متغير سابق
                            if (i > 0) {
                                previousVariation = variationsWithQuantities[i - 1];
                            }
                            break;
                        }
                    }

                    // إذا وجدنا متغيرًا سابقًا
                    if (previousVariation) {


                        // تحديث الكمية إلى الكمية المطلوبة للمتغير السابق
                        updateQuantity(previousVariation.quantity);
                    } else {
                        // إذا لم نجد متغيرًا سابقًا، نقلل الكمية بمقدار 1
                        updateQuantity(quantity - 1);
                    }
                } else {
                    // إذا لم يكن هناك متغير محدد أو كانت الكمية الحالية لا تساوي الكمية المطلوبة للمتغير المحدد
                    // نقلل الكمية بمقدار 1
                    updateQuantity(quantity - 1);
                }
            }
        });

        // معالجة نقر زر الزيادة
        $plusBtn.on('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            if (quantity < maxQuantity) {
                updateQuantity(quantity + 1);
            }
        });

        // معالجة تغييرات الإدخال المباشر
        $quantityInput.on('input', function() {
            var newQuantity = parseInt($(this).val()) || 1;
            if (newQuantity < minQuantity) newQuantity = minQuantity;
            if (newQuantity > maxQuantity) newQuantity = maxQuantity;

            // تحديث عرض الكمية في ملخص الطلب مباشرة
            $('.quantity-number').text(newQuantity);

            updateQuantity(newQuantity);
        });

        // تعطيل إدخال الحروف في حقل الكمية
        $quantityInput.on('keypress', function(e) {
            if (e.which < 48 || e.which > 57) {
                e.preventDefault();
            }
        });

        // تحديث السعر الأولي
        updateTotalPrice($form);
    }

    /**
     * Format price with currency
     *
     * @param {number} price Price to format
     * @return {string} Formatted price
     */
    function formatPrice(price) {
        // استخدام إعدادات WooCommerce للعملة إذا كانت متوفرة من formElrakami
        if (typeof formElrakami !== 'undefined' && formElrakami.currency_symbol) {
            var symbol = formElrakami.currency_symbol;
            var position = formElrakami.currency_position || 'left';
            var decimals = parseInt(formElrakami.price_decimals) || 2;
            var decimal_sep = formElrakami.price_decimal_separator || '.';
            var thousand_sep = formElrakami.price_thousand_separator || ',';

            var formatted_price = price.toFixed(decimals);
            if (decimal_sep !== '.') {
                formatted_price = formatted_price.replace('.', decimal_sep);
            }
            if (thousand_sep) {
                formatted_price = formatted_price.replace(/\B(?=(\d{3})+(?!\d))/g, thousand_sep);
            }

            switch (position) {
                case 'left':
                    return symbol + formatted_price;
                case 'right':
                    return formatted_price + symbol;
                case 'left_space':
                    return symbol + ' ' + formatted_price;
                case 'right_space':
                    return formatted_price + ' ' + symbol;
                default:
                    return symbol + formatted_price;
            }
        }

        // استخدام إعدادات WooCommerce للعملة إذا كانت متوفرة من woocommerce_params
        if (typeof woocommerce_params !== 'undefined' && woocommerce_params.currency_format_symbol) {
            var symbol = woocommerce_params.currency_format_symbol;
            var position = woocommerce_params.currency_format_symbol_pos || 'left';
            var decimals = parseInt(woocommerce_params.currency_format_num_decimals) || 2;
            var decimal_sep = woocommerce_params.currency_format_decimal_sep || '.';
            var thousand_sep = woocommerce_params.currency_format_thousand_sep || ',';

            var formatted_price = price.toFixed(decimals);
            if (decimal_sep !== '.') {
                formatted_price = formatted_price.replace('.', decimal_sep);
            }
            if (thousand_sep) {
                formatted_price = formatted_price.replace(/\B(?=(\d{3})+(?!\d))/g, thousand_sep);
            }

            switch (position) {
                case 'left':
                    return symbol + formatted_price;
                case 'right':
                    return formatted_price + symbol;
                case 'left_space':
                    return symbol + ' ' + formatted_price;
                case 'right_space':
                    return formatted_price + ' ' + symbol;
                default:
                    return symbol + formatted_price;
            }
        }

        // كحل أخير، استخدم تنسيق افتراضي
        return price.toFixed(2) + ' د.ج';
    }

    /**
     * جدولة حفظ مسودة النموذج
     *
     * @param {jQuery} $form النموذج الذي يتم حفظه
     */
    function scheduleFormDraftSave($form) {
        // إلغاء المؤقت السابق إن وجد
        if (draftSaveTimer) {
            clearTimeout(draftSaveTimer);
        }

        // ضبط مؤقت جديد
        draftSaveTimer = setTimeout(function() {
            saveFormDraft($form);
        }, draftSaveDelay);
    }

    /**
     * جدولة حفظ الطلب المتروك مع تأخير أطول
     *
     * @param {jQuery} $form النموذج الذي يتم حفظه
     */
    function scheduleAbandonedOrderSave($form) {
        // إلغاء أي مؤقت سابق للطلب المتروك
        if (abandonedOrderSaveTimeout) {
            clearTimeout(abandonedOrderSaveTimeout);
        }

        // الحصول على تأخير الحفظ من الإعدادات (افتراضي 30 ثانية)
        var saveDelay = (typeof formElrakami !== 'undefined' && formElrakami.abandoned_order_delay)
                       ? parseInt(formElrakami.abandoned_order_delay) * 1000
                       : 30000;

        // تعيين مؤقت جديد مع التأخير المحدد
        abandonedOrderSaveTimeout = setTimeout(function() {
            // التحقق مرة أخرى من أن المستخدم لم يكمل الطلب من السلة
            if (cartButtonClicked && hasUnsavedChanges) {
                console.log('حفظ الطلب المتروك بعد التأخير');
                saveFormDraft($form);
            }
        }, saveDelay);
    }

    /**
     * تهيئة الشريط اللاصق (المثبت) في أسفل الصفحة
     */
    function initStickyBar() {
        // البحث عن عناصر الشريط اللاصق
        var $stickyBar = $('.pexlat-form-sticky-bar');

        if ($stickyBar.length > 0) {
            // البحث عن نماذج الطلب
            var $forms = $('.pexlat-form-form');

            // التحقق من إعدادات الشريط المثبت
            var alwaysVisible = $stickyBar.data('always-visible') === 'yes';
            var buttonSubmit = $stickyBar.data('button-submit') === 'yes';



            // تعيين تفاعلات الشريط اللاصق
            $(window).on('scroll', function() {
                // إذا كان الشريط مضبوطًا ليكون مرئيًا دائمًا، فلا داعي لإخفائه
                if (alwaysVisible) {
                    $stickyBar.removeClass('hidden');
                    return;
                }

                var scrollTop = $(window).scrollTop();
                var windowHeight = $(window).height();

                // التحقق مما إذا كان أي نموذج مرئي في الشاشة
                var isFormVisible = false;

                $forms.each(function() {
                    var $form = $(this);
                    var formTop = $form.offset().top;
                    var formBottom = formTop + $form.outerHeight();

                    // التحقق مما إذا كان النموذج مرئيًا في الشاشة
                    if (
                        (scrollTop <= formBottom && scrollTop + windowHeight >= formTop) ||
                        (formTop >= scrollTop && formTop <= scrollTop + windowHeight) ||
                        (formBottom >= scrollTop && formBottom <= scrollTop + windowHeight)
                    ) {
                        isFormVisible = true;
                        return false; // الخروج من الحلقة
                    }
                });

                // إظهار أو إخفاء الشريط بناءً على رؤية النموذج
                if (isFormVisible && !alwaysVisible) {
                    // إخفاء الشريط عندما يكون النموذج مرئيًا (إلا إذا كان مضبوطًا ليكون مرئيًا دائمًا)
                    $stickyBar.addClass('hidden');
                } else {
                    // إظهار الشريط عندما لا يكون النموذج مرئيًا
                    $stickyBar.removeClass('hidden');
                }
            });

            // تهيئة زر الطلب في الشريط اللاصق
            $('.pexlat-form-sticky-bar-button').on('click', function() {
                var targetFormId = $(this).attr('id').replace('pexlat-form-sticky-order-', '');
                var $targetForm = $('#pexlat-form-' + targetFormId);

                if ($targetForm.length > 0) {
                    if (buttonSubmit) {
                        // إذا كان الزر مضبوطًا ليقوم بالطلب، قم بتنفيذ نفس وظيفة زر الطلب الرئيسي
                        var $submitButton = $targetForm.find('.pexlat-form-submit');
                        if ($submitButton.length > 0) {
                            // التحقق من صحة النموذج قبل إرسال الطلب
                            if (!validateForm($targetForm)) {
                                // النموذج غير صالح، تم التمرير إلى الحقل الأول الذي يحتوي على خطأ بالفعل في دالة validateForm
                                return;
                            }

                            // النموذج صالح، قم بإرسال الطلب
                            $submitButton.trigger('click');
                        }
                    } else {
                        // التمرير إلى النموذج بشكل مباشر بدون تأخير
                        $('html, body').animate({
                            scrollTop: $targetForm.offset().top - 20
                        }, 300, 'linear');
                    }
                }
            });

            // تشغيل حدث التمرير مرة واحدة لتحديد حالة الشريط المثبت عند تحميل الصفحة
            $(window).trigger('scroll');
        }
    }

    /**
     * حفظ مسودة النموذج
     *
     * @param {jQuery} $form النموذج الذي يتم حفظه
     */
    function saveFormDraft($form) {
        // منع الحفظ المتكرر
        if (isDraftSaving) {
            return;
        }

        // تعيين علم أن عملية الحفظ جارية
        isDraftSaving = true;

        // جمع بيانات النموذج
        var formData = $form.serialize();

        // التحقق مما إذا كانت البيانات قد تغيرت منذ آخر حفظ
        if (lastSavedData === formData) {
            isDraftSaving = false;
            return;
        }

        // تحقق من وجود بيانات كافية لحفظها
        // على الأقل أحد الحقول المهمة يجب أن يكون مملوءًا
        var $fullName = $form.find('input[name="full_name"]');
        var $phone = $form.find('input[name="phone"]');

        if (($fullName.length > 0 && !$fullName.val()) && ($phone.length > 0 && !$phone.val())) {
            // لا توجد بيانات كافية لحفظها
            isDraftSaving = false;
            return;
        }



        // إضافة نوع العملية لبيانات النموذج
        formData += '&action=pexlat_form_save_draft';

        // إرسال البيانات للحفظ كمسودة
        $.ajax({
            url: formElrakami.ajaxurl,
            type: 'POST',
            data: formData,
            dataType: 'json',
            success: function(response) {
                isDraftSaving = false;

                if (response.success) {
                    lastSavedData = formData; // تحديث البيانات المحفوظة الأخيرة
                    hasUnsavedChanges = false; // تم حفظ التغييرات

                    // إلغاء مؤقت الطلب المتروك إذا تم الحفظ بنجاح
                    if (abandonedOrderSaveTimeout) {
                        clearTimeout(abandonedOrderSaveTimeout);
                        abandonedOrderSaveTimeout = null;
                    }
                } else {
                    console.error('فشل حفظ المسودة:', response.data);
                }
            },
            error: function(xhr, status, error) {
                isDraftSaving = false;
                console.error('خطأ في حفظ المسودة:', error);
            }
        });
    }



    /**
     * عرض رسالة تنبيه
     *
     * @param {string} message نص الرسالة
     * @param {string} type نوع الرسالة (success, error, info, warning)
     */
    function showToast(message, type) {
        // إزالة أي رسائل سابقة
        $('.pexlat-form-toast').remove();

        // إنشاء عنصر التنبيه
        var $toast = $('<div class="pexlat-form-toast ' + type + '">' + message + '</div>');
        $('body').append($toast);

        // إظهار التنبيه
        $toast.fadeIn(300);

        // إخفاء التنبيه بعد 3 ثوان
        setTimeout(function() {
            $toast.fadeOut(300, function() {
                $toast.remove();
            });
        }, 3000);
    }

    /**
     * تهيئة وظيفة طي/فتح ملخص الطلب
     */
    function initializeSummaryToggle() {
        // البحث عن جميع عناصر summary-header
        $(document).on('click', '.summary-header', function(e) {
            e.preventDefault();
            e.stopPropagation();

            var $header = $(this);
            var $summaryContent = null;
            var $toggleIcon = $header.find('.toggle-icon, .summary-toggle-icon, .fas, .fa');

            // البحث عن محتوى الملخص بطرق مختلفة
            // الطريقة الأولى: البحث في العنصر التالي مباشرة
            $summaryContent = $header.next('.order-summary-content, .summary-content, .order-summary-details');

            // الطريقة الثانية: البحث في نفس الحاوي
            if ($summaryContent.length === 0) {
                $summaryContent = $header.siblings('.order-summary-content, .summary-content, .order-summary-details');
            }

            // الطريقة الثالثة: البحث في الحاوي الأب
            if ($summaryContent.length === 0) {
                $summaryContent = $header.parent().find('.order-summary-content, .summary-content, .order-summary-details');
                // استبعاد العنوان نفسه
                $summaryContent = $summaryContent.not($header);
            }

            // الطريقة الرابعة: البحث عن أي div يحتوي على تفاصيل الطلب
            if ($summaryContent.length === 0) {
                $summaryContent = $header.parent().find('div').not($header).first();
            }



            if ($summaryContent.length > 0) {
                // تبديل حالة الظهور/الإخفاء مع تأثير انزلاق
                $summaryContent.slideToggle(400, function() {
                    var isVisible = $summaryContent.is(':visible');

                    // تحديث أيقونة السهم
                    if ($toggleIcon.length > 0) {
                        if (isVisible) {
                            // المحتوى مرئي - سهم لأعلى
                            $toggleIcon.removeClass('fa-chevron-down fa-angle-down fa-caret-down')
                                      .addClass('fa-chevron-up');
                            $header.removeClass('collapsed').addClass('expanded');
                        } else {
                            // المحتوى مخفي - سهم لأسفل
                            $toggleIcon.removeClass('fa-chevron-up fa-angle-up fa-caret-up')
                                      .addClass('fa-chevron-down');
                            $header.removeClass('expanded').addClass('collapsed');
                        }
                    }

                    // حفظ حالة الطي في localStorage
                    var summaryId = $header.attr('id') || 'default-summary';
                    localStorage.setItem('summary-collapsed-' + summaryId, !isVisible);

                });
            }
        });

        // إضافة أيقونة السهم إذا لم تكن موجودة وتطبيق الأنماط
        $('.summary-header').each(function() {
            var $header = $(this);
            var $toggleIcon = $header.find('.toggle-icon, .summary-toggle-icon, .fas, .fa');

            // إضافة أيقونة السهم إذا لم تكن موجودة
            if ($toggleIcon.length === 0) {
                $header.append('<i class="fas fa-chevron-down toggle-icon" style="margin-left: 10px; margin-right: 10px; transition: transform 0.3s ease; float: right;"></i>');
            }

            // تطبيق أنماط CSS للعنوان
            $header.css({
                'cursor': 'pointer',
                'user-select': 'none',
                'transition': 'all 0.3s ease',
                'position': 'relative'
            });

            // إضافة تأثير hover
            $header.hover(
                function() {
                    $(this).css('opacity', '0.8');
                },
                function() {
                    $(this).css('opacity', '1');
                }
            );

            // استعادة حالة الطي المحفوظة
            var summaryId = $header.attr('id') || 'default-summary';
            var isCollapsed = localStorage.getItem('summary-collapsed-' + summaryId) === 'true';

            if (isCollapsed) {
                // إخفاء المحتوى إذا كان مطوياً
                var $summaryContent = $header.next('.order-summary-content, .summary-content, .order-summary-details');
                if ($summaryContent.length === 0) {
                    $summaryContent = $header.siblings('.order-summary-content, .summary-content, .order-summary-details');
                }
                if ($summaryContent.length === 0) {
                    $summaryContent = $header.parent().find('.order-summary-content, .summary-content, .order-summary-details').not($header);
                }

                if ($summaryContent.length > 0) {
                    $summaryContent.hide();
                    $header.find('.toggle-icon, .fas, .fa').removeClass('fa-chevron-up').addClass('fa-chevron-down');
                    $header.removeClass('expanded').addClass('collapsed');
                }
            }
        });
    }

    // تهيئة وظيفة طي الملخص عند تحميل الصفحة
    initializeSummaryToggle();

    // تهيئة العرض المبسط عند تحميل الصفحة
    initializeSimpleShippingDisplay();

    /**
     * تحديث طرق التوصيل المبسطة في ملخص الطلب
     * @param {jQuery} $form النموذج
     * @param {Array} methods طرق التوصيل
     */
    function updateSimpleShippingMethods($form, methods) {
        var $simpleContainer = $form.find('.simple-shipping-methods-container');

        // التحقق من وضع العرض من إعدادات النموذج
        var shippingDisplayMode = formElrakami.shipping_display_mode || 'detailed';

        // إضافة attribute للحاوية لإخفاء العرض التفصيلي
        var $formContainer = $form.closest('.pexlat-form-container');
        $formContainer.attr('data-shipping-display', shippingDisplayMode);

        // التحقق من وجود الحاوية ووضع العرض المبسط
        if ($simpleContainer.length === 0 || shippingDisplayMode !== 'simple') {
            return;
        }

        // إذا كانت هناك طريقة واحدة فقط، لا نعرض شيئاً في العرض المبسط
        if (!methods || methods.length <= 1) {
            $simpleContainer.parent().hide();
            return;
        }

        // إظهار قسم طرق التوصيل المبسطة
        $simpleContainer.parent().show();

        var html = '';

        $.each(methods, function(i, method) {
            var methodId = method.id || method.value;
            var methodCost = method.cost || 0;
            var methodTitle = method.title || method.name;

            html += '<div class="simple-shipping-method">';
            html += '<div class="simple-shipping-method-right">';
            html += '<input type="radio" name="simple_shipping_method_option" value="' + methodId + '" data-cost="' + methodCost + '" id="simple_method_' + i + '">';
            html += '<div class="simple-shipping-method-title">' + methodTitle + '</div>';
            html += '</div>';
            html += '<div class="simple-shipping-method-price">' + formatPrice(methodCost) + '</div>';
            html += '</div>';
        });

        $simpleContainer.html(html);

        // تحديد أول طريقة تلقائياً
        $simpleContainer.find('input[type="radio"]:first').prop('checked', true);
        $simpleContainer.find('.simple-shipping-method:first').addClass('selected');

        // إضافة معالج الأحداث للاختيار
        $simpleContainer.find('input[type="radio"]').on('change', function() {
            var $selectedMethod = $(this);
            var methodId = $selectedMethod.val();
            var methodCost = $selectedMethod.data('cost');

            // تحديث الطريقة المحددة بصرياً
            $simpleContainer.find('.simple-shipping-method').removeClass('selected');
            $selectedMethod.closest('.simple-shipping-method').addClass('selected');

            // تحديث الطريقة المحددة في النموذج الأصلي
            $form.find('input[name="shipping_method_option"][value="' + methodId + '"]').prop('checked', true).trigger('change');
        });

        // إضافة معالج النقر على النص والسعر
        $simpleContainer.find('.simple-shipping-method-right, .simple-shipping-method-price').on('click', function() {
            $(this).closest('.simple-shipping-method').find('input[type="radio"]').prop('checked', true).trigger('change');
        });
    }

    /**
     * تهيئة العرض المبسط عند تحميل الصفحة
     */
    function initializeSimpleShippingDisplay() {
        // التحقق من وضع العرض من إعدادات النموذج
        var shippingDisplayMode = formElrakami.shipping_display_mode || 'detailed';

        // إضافة attribute للحاوية لإخفاء العرض التفصيلي
        $('.pexlat-form-container').each(function() {
            $(this).attr('data-shipping-display', shippingDisplayMode);
        });
    }

    /**
     * تتبع النقر على أزرار السلة
     */
    function trackCartButtonClicks() {
        // تتبع النقر على أزرار إضافة للسلة
        $(document).on('click', '.pexlat-form-add-to-cart, .cart-icon-inline', function(e) {
            cartButtonClicked = true;
            isUsingCart = true;

            // إلغاء أي مؤقت لحفظ الطلب المتروك
            if (abandonedOrderSaveTimeout) {
                clearTimeout(abandonedOrderSaveTimeout);
                abandonedOrderSaveTimeout = null;
            }

            console.log('تم النقر على زر السلة - تم تأخير حفظ الطلب المتروك');
        });

        // تتبع النقر على زر الطلب المباشر
        $(document).on('click', '.pexlat-form-submit', function(e) {
            if (!cartButtonClicked) {
                isUsingCart = false;
                console.log('تم النقر على زر الطلب المباشر');
            }
        });
    }

    /**
     * إضافة آلية حفظ الطلب المتروك عند مغادرة الصفحة
     */
    function initPageUnloadHandler() {
        $(window).on('beforeunload', function(e) {
            // الحصول على وضع حفظ الطلب المتروك من الإعدادات
            var saveMode = (typeof formElrakami !== 'undefined' && formElrakami.abandoned_order_save_mode)
                          ? formElrakami.abandoned_order_save_mode
                          : 'smart';

            // التحقق من وجود تغييرات غير محفوظة
            var shouldSave = hasUnsavedChanges && !pageUnloading;

            // تحديد ما إذا كان يجب الحفظ بناءً على الوضع
            if (saveMode === 'on_exit') {
                // حفظ دائماً عند المغادرة
                shouldSave = shouldSave && true;
            } else if (saveMode === 'smart') {
                // حفظ فقط إذا لم يتم النقر على زر السلة أو تم النقر ولكن لم يكتمل الطلب
                shouldSave = shouldSave && (!cartButtonClicked || (cartButtonClicked && hasUnsavedChanges));
            } else {
                // وضع فوري - لا نحفظ عند المغادرة لأنه تم الحفظ مسبقاً
                shouldSave = false;
            }

            if (shouldSave) {
                pageUnloading = true;

                // محاولة حفظ الطلب المتروك بسرعة
                var $forms = $('.pexlat-form-form');
                if ($forms.length > 0) {
                    $forms.each(function() {
                        var $form = $(this);
                        saveAbandonedOrderSync($form);
                    });
                }

                // إرجاع رسالة تحذير (اختياري)
                return 'لديك تغييرات غير محفوظة. هل تريد المغادرة؟';
            }
        });

        // تتبع مغادرة الصفحة الفعلية
        $(window).on('unload', function() {
            pageUnloading = true;
        });
    }

    /**
     * حفظ الطلب المتروك بشكل متزامن (للاستخدام عند مغادرة الصفحة)
     */
    function saveAbandonedOrderSync($form) {
        if (!hasUnsavedChanges || cartButtonClicked) {
            return;
        }

        var formData = $form.serialize();
        formData += '&action=pexlat_form_save_draft';

        // استخدام sendBeacon للإرسال المتزامن
        if (navigator.sendBeacon) {
            var blob = new Blob([formData], {type: 'application/x-www-form-urlencoded'});
            navigator.sendBeacon(formElrakami.ajaxurl, blob);
        } else {
            // fallback للمتصفحات القديمة
            $.ajax({
                url: formElrakami.ajaxurl,
                type: 'POST',
                data: formData,
                async: false // متزامن فقط كحل أخير
            });
        }
    }

    /**
     * إعادة تعيين حالة السلة والطلبات المتروكة
     */
    function resetCartState() {
        isUsingCart = false;
        cartButtonClicked = false;
        hasUnsavedChanges = false;

        // إلغاء أي مؤقتات معلقة
        if (abandonedOrderSaveTimeout) {
            clearTimeout(abandonedOrderSaveTimeout);
            abandonedOrderSaveTimeout = null;
        }

        if (draftSaveTimer) {
            clearTimeout(draftSaveTimer);
            draftSaveTimer = null;
        }
    }

    // تهيئة الوظائف الجديدة
    trackCartButtonClicks();
    initPageUnloadHandler();

});