# تحسينات نظام الطلبات المتروكة

## المشكلة الأصلية

كان النظام السابق يعاني من المشاكل التالية:
1. إنشاء طلبات متكررة عند استخدام السلة
2. عدم تحديث الطلبات الموجودة
3. حفظ الطلبات المتروكة فوراً دون مراعاة سلوك المستخدم
4. عدم وجود آلية لحذف الطلبات القديمة

## الحلول المطبقة

### 1. تتبع حالة السلة (JavaScript)

**الملفات المعدلة:**
- `public/js/pexlat-form-public.js`

**التحسينات:**
- إضافة متغيرات لتتبع استخدام السلة
- تتبع النقر على أزرار السلة
- تأخير حفظ الطلب المتروك عند استخدام السلة
- آلية beforeunload لحفظ الطلب عند مغادرة الصفحة

```javascript
// متغيرات جديدة
var isUsingCart = false;
var cartButtonClicked = false;
var abandonedOrderSaveTimeout;
var hasUnsavedChanges = false;
var pageUnloading = false;
```

### 2. تحسين آلية حفظ الطلبات المتروكة (PHP)

**الملفات المعدلة:**
- `includes/class-pexlat-form-form-handler.php`
- `includes/class-custom-cart-system.php`

**التحسينات:**
- البحث عن طلب مسودة موجود بناءً على رقم الهاتف + عنوان IP
- تحديث نفس الطلب بدلاً من إنشاء طلبات جديدة
- التحقق من صحة الطلبات وحذف القديمة
- آلية تنظيف تلقائية للطلبات القديمة

```php
// مفتاح المسودة الجديد
$draft_key = 'pexlat_form_draft_' . md5($phone . '_' . $ip_address);
```

### 3. إعدادات تحكم جديدة

**الملفات المعدلة:**
- `admin/partials/settings-page.php`
- `includes/class-pexlat-form-admin.php`
- `includes/class-pexlat-form.php`

**الإعدادات الجديدة:**
1. **وضع حفظ الطلب المتروك:**
   - فوري: حفظ فور تغيير الحقول
   - ذكي: تأخير عند استخدام السلة (موصى به)
   - عند المغادرة: حفظ فقط عند مغادرة الصفحة

2. **تأخير حفظ الطلب:** قابل للتخصيص (5-300 ثانية)

3. **مدة الاحتفاظ:** قابلة للتخصيص (1-168 ساعة)

## الوظائف الجديدة

### JavaScript Functions

```javascript
// تتبع النقر على أزرار السلة
function trackCartButtonClicks()

// جدولة حفظ الطلب المتروك مع تأخير
function scheduleAbandonedOrderSave($form)

// حفظ الطلب عند مغادرة الصفحة
function initPageUnloadHandler()

// حفظ متزامن للطلب المتروك
function saveAbandonedOrderSync($form)

// إعادة تعيين حالة السلة
function resetCartState()
```

### PHP Functions

```php
// البحث عن طلب موجود أو إنشاء جديد
private function find_or_create_cart_order($cart)

// حذف مفتاح المسودة
private function cleanup_draft_key($customer_data)

// تنظيف المسودات القديمة
public static function cleanup_old_drafts()
```

## آلية العمل الجديدة

### السيناريو 1: الطلب المباشر
1. المستخدم يملأ النموذج
2. يتم حفظ الطلب المتروك حسب الإعدادات
3. عند النقر على "طلب الآن" يتم إنشاء طلب جديد
4. يتم حذف مفتاح المسودة

### السيناريو 2: استخدام السلة (الوضع الذكي)
1. المستخدم يملأ النموذج
2. ينقر على "إضافة للسلة"
3. يتم تأخير حفظ الطلب المتروك
4. إذا لم يكمل الطلب خلال المدة المحددة، يتم الحفظ
5. عند إتمام الطلب من السلة، يتم تحديث نفس الطلب

### السيناريو 3: مغادرة الصفحة
1. المستخدم يملأ النموذج
2. يغادر الصفحة دون إكمال الطلب
3. يتم حفظ الطلب المتروك تلقائياً

## الفوائد

1. **تقليل الطلبات المتكررة:** لا يتم إنشاء طلبات جديدة عند استخدام السلة
2. **تحسين تجربة المستخدم:** عدم فقدان البيانات عند مغادرة الصفحة
3. **تحسين الأداء:** تحديث الطلبات الموجودة بدلاً من إنشاء جديدة
4. **مرونة في التحكم:** إعدادات قابلة للتخصيص حسب احتياجات المتجر
5. **تنظيف تلقائي:** حذف الطلبات القديمة لتوفير مساحة قاعدة البيانات

## الإعدادات الافتراضية

```php
'pexlat_form_abandoned_order_save_mode' => 'smart'
'pexlat_form_abandoned_order_delay' => 30 // ثانية
'pexlat_form_abandoned_order_cleanup_hours' => 24 // ساعة
```

## التوافق

- ✅ متوافق مع جميع أنواع المنتجات
- ✅ متوافق مع نظام السلة الحالي
- ✅ متوافق مع إعدادات الطلبات المتروكة الحالية
- ✅ لا يؤثر على الوظائف الموجودة

## الاختبار

راجع ملف `test-abandoned-orders.md` للحصول على دليل اختبار شامل.

## الصيانة

- يتم تنظيف الطلبات القديمة تلقائياً
- يمكن تشغيل التنظيف يدوياً: `do_action('pexlat_form_cleanup_old_drafts')`
- مراقبة سجلات الأخطاء للتأكد من عمل النظام بشكل صحيح
